#!/usr/bin/env python3
"""
Deployment script for the HL7 Validation and Enhancement Pipeline
Tests the pipeline against the current HL7 message corpus
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime

# Add current directory to path for imports
sys.path.append('.')

from pipeline_orchestrator import PipelineOrchestrator, PipelineConfig

def main():
    """Main deployment function"""
    print("="*60)
    print("HL7 VALIDATION AND ENHANCEMENT PIPELINE DEPLOYMENT")
    print("="*60)
    print(f"Deployment started at: {datetime.now().isoformat()}")
    print()
    
    # Configuration
    source_dir = "enhancedHl7"
    output_dir = "pipeline_output"
    quarantine_dir = "pipeline_quarantine"
    
    # Verify source directory exists
    if not Path(source_dir).exists():
        print(f"❌ Source directory '{source_dir}' does not exist!")
        print("Please ensure the enhancedHl7 directory contains HL7 files to process.")
        return 1
    
    # Count files in source directory
    hl7_files = list(Path(source_dir).glob("**/*.hl7"))
    if not hl7_files:
        print(f"❌ No HL7 files found in '{source_dir}'!")
        return 1
    
    print(f"📁 Source directory: {source_dir}")
    print(f"📁 Output directory: {output_dir}")
    print(f"📁 Quarantine directory: {quarantine_dir}")
    print(f"📊 Found {len(hl7_files)} HL7 files to process")
    print()
    
    # Create pipeline configuration
    config = PipelineConfig(
        source_directory=source_dir,
        output_directory=output_dir,
        quarantine_directory=quarantine_dir,
        file_patterns=["*.hl7"],
        max_enhancement_retries=3,
        enable_auto_retry=True,
        batch_size=50,  # Process in batches of 50
        parallel_processing=False,
        backup_original_files=True,
        generate_reports=True,
        log_level="INFO"
    )
    
    print("🚀 Starting pipeline execution...")
    print()
    
    try:
        # Create and run pipeline
        start_time = time.time()
        orchestrator = PipelineOrchestrator(config)
        result = orchestrator.run_pipeline()
        end_time = time.time()
        
        # Display results
        print()
        print("="*60)
        print("PIPELINE EXECUTION RESULTS")
        print("="*60)
        
        execution_summary = result['execution_summary']
        performance_metrics = result['performance_metrics']
        
        print(f"✅ Pipeline completed successfully!")
        print(f"⏱️  Total execution time: {end_time - start_time:.2f} seconds")
        print()
        
        print("📊 PROCESSING STATISTICS:")
        print(f"   • Total files processed: {execution_summary['total_files_processed']}")
        print(f"   • Files successfully enhanced: {execution_summary['files_successfully_enhanced']}")
        print(f"   • Files already valid: {execution_summary['files_already_valid']}")
        print(f"   • Files quarantined: {execution_summary['files_quarantined']}")
        print()
        
        print("📈 PERFORMANCE METRICS:")
        print(f"   • Success rate: {performance_metrics['success_rate_percentage']:.2f}%")
        print(f"   • Average processing time per file: {performance_metrics['average_processing_time_per_file']:.3f} seconds")
        print(f"   • Files processed per second: {performance_metrics['files_per_second']:.2f}")
        print()
        
        # Quarantine summary
        quarantine_summary = result.get('quarantine_summary', {})
        if quarantine_summary.get('total_quarantined', 0) > 0:
            print("⚠️  QUARANTINE SUMMARY:")
            print(f"   • Total quarantined: {quarantine_summary['total_quarantined']}")
            print(f"   • Manual review required: {quarantine_summary['manual_review_required']}")
            print(f"   • Retry eligible: {quarantine_summary['retry_eligible']}")
            
            # Show top quarantine categories
            by_category = quarantine_summary.get('by_category', {})
            if by_category:
                print("   • Top quarantine categories:")
                sorted_categories = sorted(by_category.items(), key=lambda x: x[1], reverse=True)
                for category, count in sorted_categories[:5]:
                    if count > 0:
                        print(f"     - {category}: {count}")
            print()
        
        # Recommendations
        recommendations = result.get('recommendations', [])
        if recommendations:
            print("💡 RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations, 1):
                print(f"   {i}. {rec}")
            print()
        
        # Output directories
        print("📁 OUTPUT LOCATIONS:")
        print(f"   • Enhanced files: {output_dir}/")
        print(f"   • Quarantined files: {quarantine_dir}/")
        print(f"   • Reports: {output_dir}/reports/")
        print()
        
        # Success criteria
        success_rate = performance_metrics['success_rate_percentage']
        if success_rate >= 95:
            print("🎉 EXCELLENT: Pipeline achieved >95% success rate!")
        elif success_rate >= 90:
            print("✅ GOOD: Pipeline achieved >90% success rate!")
        elif success_rate >= 80:
            print("⚠️  ACCEPTABLE: Pipeline achieved >80% success rate, but consider improvements.")
        else:
            print("❌ NEEDS IMPROVEMENT: Pipeline success rate is below 80%. Review quarantine reasons.")
        
        # Check for reports
        reports_dir = Path(output_dir) / "reports"
        if reports_dir.exists():
            report_files = list(reports_dir.glob("*.json"))
            if report_files:
                print(f"📋 Generated {len(report_files)} detailed reports")
        
        print()
        print("="*60)
        print("DEPLOYMENT COMPLETED SUCCESSFULLY")
        print("="*60)
        
        return 0
        
    except Exception as e:
        print()
        print("="*60)
        print("DEPLOYMENT FAILED")
        print("="*60)
        print(f"❌ Error: {str(e)}")
        print()
        print("Please check the logs for more details.")
        return 1

def validate_environment():
    """Validate that the environment is ready for deployment"""
    print("🔍 Validating environment...")
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        return False
    
    # Check required modules
    required_modules = ['hl7', 'pathlib', 'json', 'logging']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ Missing required modules: {', '.join(missing_modules)}")
        print("Please install missing modules with: pip install -r requirements.txt")
        return False
    
    print("✅ Environment validation passed")
    return True

if __name__ == "__main__":
    print("HL7 Pipeline Deployment Script")
    print("=" * 40)
    
    # Validate environment
    if not validate_environment():
        sys.exit(1)
    
    # Run deployment
    exit_code = main()
    sys.exit(exit_code)
