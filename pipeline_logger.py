#!/usr/bin/env python3
"""
Comprehensive Logging and Reporting System for HL7 Pipeline
Provides detailed logging, metrics tracking, and exportable reports
"""

import logging
import json
import csv
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from collections import defaultdict, deque

class LogLevel(Enum):
    """Log levels for pipeline events"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class EventType(Enum):
    """Types of pipeline events"""
    PIPELINE_START = "PIPELINE_START"
    PIPELINE_END = "PIPELINE_END"
    FILE_PROCESSING_START = "FILE_PROCESSING_START"
    FILE_PROCESSING_END = "FILE_PROCESSING_END"
    VALIDATION_COMPLETE = "VALIDATION_COMPLETE"
    ENHANCEMENT_START = "ENHANCEMENT_START"
    ENHANCEMENT_COMPLETE = "ENHANCEMENT_COMPLETE"
    QUARANTINE_EVENT = "QUARANTINE_EVENT"
    RETRY_ATTEMPT = "RETRY_ATTEMPT"
    ERROR_EVENT = "ERROR_EVENT"
    PERFORMANCE_METRIC = "PERFORMANCE_METRIC"

@dataclass
class LogEvent:
    """Represents a single log event"""
    timestamp: str
    event_type: EventType
    level: LogLevel
    message: str
    file_path: Optional[str] = None
    duration_ms: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class PerformanceMetrics:
    """Performance metrics for pipeline operations"""
    total_processing_time: float = 0.0
    validation_time: float = 0.0
    enhancement_time: float = 0.0
    quarantine_time: float = 0.0
    file_io_time: float = 0.0
    files_per_second: float = 0.0
    average_file_size: float = 0.0
    memory_usage_mb: float = 0.0

class PipelineLogger:
    """Comprehensive logging system for the HL7 pipeline"""
    
    def __init__(self, log_dir: str, config: Optional[Dict] = None):
        """Initialize the pipeline logger"""
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        self.config = config or {}
        self.max_events_in_memory = self.config.get('max_events_in_memory', 10000)
        self.auto_flush_interval = self.config.get('auto_flush_interval', 300)  # 5 minutes
        self.enable_performance_tracking = self.config.get('enable_performance_tracking', True)
        
        # Event storage
        self.events = deque(maxlen=self.max_events_in_memory)
        self.performance_metrics = PerformanceMetrics()
        self.event_counts = defaultdict(int)
        self.error_counts = defaultdict(int)
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Setup logging
        self.logger = self._setup_file_logging()
        
        # Performance tracking
        self.operation_timers = {}
        self.file_processing_stats = {}
        
        # Auto-flush timer
        self._start_auto_flush_timer()
    
    def _setup_file_logging(self) -> logging.Logger:
        """Setup file-based logging"""
        logger = logging.getLogger('pipeline_logger')
        logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Create file handlers
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Main log file
        main_log_file = self.log_dir / f"pipeline_{timestamp}.log"
        main_handler = logging.FileHandler(main_log_file)
        main_handler.setLevel(logging.INFO)
        
        # Error log file
        error_log_file = self.log_dir / f"pipeline_errors_{timestamp}.log"
        error_handler = logging.FileHandler(error_log_file)
        error_handler.setLevel(logging.ERROR)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        main_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)
        
        logger.addHandler(main_handler)
        logger.addHandler(error_handler)
        
        return logger
    
    def log_event(self, 
                  event_type: EventType, 
                  level: LogLevel, 
                  message: str,
                  file_path: Optional[str] = None,
                  duration_ms: Optional[float] = None,
                  metadata: Optional[Dict[str, Any]] = None):
        """Log a pipeline event"""
        
        event = LogEvent(
            timestamp=datetime.utcnow().isoformat() + 'Z',
            event_type=event_type,
            level=level,
            message=message,
            file_path=file_path,
            duration_ms=duration_ms,
            metadata=metadata or {}
        )
        
        with self.lock:
            self.events.append(event)
            self.event_counts[event_type.value] += 1
            
            if level in [LogLevel.ERROR, LogLevel.CRITICAL]:
                self.error_counts[event_type.value] += 1
        
        # Log to file
        log_level = getattr(logging, level.value)
        log_message = f"[{event_type.value}] {message}"
        if file_path:
            log_message += f" (File: {file_path})"
        if duration_ms:
            log_message += f" (Duration: {duration_ms:.2f}ms)"
        
        self.logger.log(log_level, log_message)
    
    def start_operation_timer(self, operation_id: str):
        """Start timing an operation"""
        if self.enable_performance_tracking:
            self.operation_timers[operation_id] = time.time()
    
    def end_operation_timer(self, operation_id: str) -> float:
        """End timing an operation and return duration in milliseconds"""
        if not self.enable_performance_tracking or operation_id not in self.operation_timers:
            return 0.0
        
        duration = (time.time() - self.operation_timers[operation_id]) * 1000
        del self.operation_timers[operation_id]
        return duration
    
    def log_file_processing_start(self, file_path: str, file_size: int):
        """Log start of file processing"""
        self.start_operation_timer(f"file_processing_{file_path}")
        
        self.log_event(
            EventType.FILE_PROCESSING_START,
            LogLevel.INFO,
            f"Started processing file: {file_path}",
            file_path=file_path,
            metadata={'file_size': file_size}
        )
    
    def log_file_processing_end(self, file_path: str, success: bool, enhancement_actions: int = 0):
        """Log end of file processing"""
        duration = self.end_operation_timer(f"file_processing_{file_path}")
        
        status = "SUCCESS" if success else "FAILED"
        message = f"Completed processing file: {file_path} ({status})"
        
        self.log_event(
            EventType.FILE_PROCESSING_END,
            LogLevel.INFO if success else LogLevel.WARNING,
            message,
            file_path=file_path,
            duration_ms=duration,
            metadata={
                'success': success,
                'enhancement_actions': enhancement_actions
            }
        )
        
        # Update performance metrics
        if self.enable_performance_tracking:
            with self.lock:
                self.file_processing_stats[file_path] = {
                    'duration_ms': duration,
                    'success': success,
                    'enhancement_actions': enhancement_actions
                }
    
    def log_validation_result(self, file_path: str, is_valid: bool, issue_count: int, duration_ms: float):
        """Log validation result"""
        status = "VALID" if is_valid else "INVALID"
        message = f"Validation completed: {file_path} ({status}, {issue_count} issues)"
        
        self.log_event(
            EventType.VALIDATION_COMPLETE,
            LogLevel.INFO if is_valid else LogLevel.WARNING,
            message,
            file_path=file_path,
            duration_ms=duration_ms,
            metadata={
                'is_valid': is_valid,
                'issue_count': issue_count
            }
        )
        
        # Update performance metrics
        if self.enable_performance_tracking:
            with self.lock:
                self.performance_metrics.validation_time += duration_ms / 1000
    
    def log_enhancement_result(self, file_path: str, success: bool, actions_count: int, duration_ms: float):
        """Log enhancement result"""
        status = "SUCCESS" if success else "FAILED"
        message = f"Enhancement completed: {file_path} ({status}, {actions_count} actions)"
        
        self.log_event(
            EventType.ENHANCEMENT_COMPLETE,
            LogLevel.INFO if success else LogLevel.WARNING,
            message,
            file_path=file_path,
            duration_ms=duration_ms,
            metadata={
                'success': success,
                'actions_count': actions_count
            }
        )
        
        # Update performance metrics
        if self.enable_performance_tracking:
            with self.lock:
                self.performance_metrics.enhancement_time += duration_ms / 1000
    
    def log_quarantine_event(self, file_path: str, category: str, reason: str, retry_count: int):
        """Log quarantine event"""
        message = f"File quarantined: {file_path} (Category: {category}, Reason: {reason})"
        
        self.log_event(
            EventType.QUARANTINE_EVENT,
            LogLevel.WARNING,
            message,
            file_path=file_path,
            metadata={
                'category': category,
                'reason': reason,
                'retry_count': retry_count
            }
        )
    
    def log_error(self, error_type: str, message: str, file_path: Optional[str] = None, exception: Optional[Exception] = None):
        """Log error event"""
        error_message = f"[{error_type}] {message}"
        if exception:
            error_message += f" - Exception: {str(exception)}"
        
        self.log_event(
            EventType.ERROR_EVENT,
            LogLevel.ERROR,
            error_message,
            file_path=file_path,
            metadata={
                'error_type': error_type,
                'exception_type': type(exception).__name__ if exception else None,
                'exception_message': str(exception) if exception else None
            }
        )
    
    def log_pipeline_start(self, config: Dict[str, Any]):
        """Log pipeline start"""
        self.log_event(
            EventType.PIPELINE_START,
            LogLevel.INFO,
            "HL7 Pipeline started",
            metadata={'config': config}
        )
    
    def log_pipeline_end(self, stats: Dict[str, Any]):
        """Log pipeline end"""
        self.log_event(
            EventType.PIPELINE_END,
            LogLevel.INFO,
            "HL7 Pipeline completed",
            metadata={'final_stats': stats}
        )
    
    def update_performance_metrics(self, **kwargs):
        """Update performance metrics"""
        if not self.enable_performance_tracking:
            return
        
        with self.lock:
            for key, value in kwargs.items():
                if hasattr(self.performance_metrics, key):
                    setattr(self.performance_metrics, key, value)
    
    def get_event_summary(self) -> Dict[str, Any]:
        """Get summary of logged events"""
        with self.lock:
            total_events = len(self.events)
            recent_events = list(self.events)[-100:]  # Last 100 events
            
            return {
                'total_events': total_events,
                'event_counts': dict(self.event_counts),
                'error_counts': dict(self.error_counts),
                'recent_events': [asdict(event) for event in recent_events],
                'performance_metrics': asdict(self.performance_metrics)
            }
    
    def export_events_to_json(self, output_path: str, include_all: bool = True):
        """Export events to JSON file"""
        with self.lock:
            events_to_export = list(self.events) if include_all else list(self.events)[-1000:]
        
        export_data = {
            'export_timestamp': datetime.utcnow().isoformat() + 'Z',
            'total_events': len(events_to_export),
            'events': [asdict(event) for event in events_to_export],
            'summary': self.get_event_summary()
        }
        
        with open(output_path, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
    
    def export_events_to_csv(self, output_path: str):
        """Export events to CSV file"""
        with self.lock:
            events_to_export = list(self.events)
        
        with open(output_path, 'w', newline='') as f:
            writer = csv.writer(f)
            
            # Header
            writer.writerow([
                'Timestamp', 'Event Type', 'Level', 'Message', 
                'File Path', 'Duration (ms)', 'Metadata'
            ])
            
            # Events
            for event in events_to_export:
                writer.writerow([
                    event.timestamp,
                    event.event_type.value,
                    event.level.value,
                    event.message,
                    event.file_path or '',
                    event.duration_ms or '',
                    json.dumps(event.metadata) if event.metadata else ''
                ])
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        with self.lock:
            file_stats = list(self.file_processing_stats.values())
        
        if not file_stats:
            return {'message': 'No performance data available'}
        
        # Calculate statistics
        total_files = len(file_stats)
        successful_files = sum(1 for stat in file_stats if stat['success'])
        total_duration = sum(stat['duration_ms'] for stat in file_stats)
        total_actions = sum(stat['enhancement_actions'] for stat in file_stats)
        
        avg_duration = total_duration / total_files if total_files > 0 else 0
        success_rate = (successful_files / total_files * 100) if total_files > 0 else 0
        
        return {
            'performance_summary': {
                'total_files_processed': total_files,
                'successful_files': successful_files,
                'success_rate_percentage': success_rate,
                'total_processing_time_ms': total_duration,
                'average_processing_time_ms': avg_duration,
                'total_enhancement_actions': total_actions,
                'files_per_second': total_files / (total_duration / 1000) if total_duration > 0 else 0
            },
            'detailed_metrics': asdict(self.performance_metrics),
            'event_statistics': dict(self.event_counts),
            'error_statistics': dict(self.error_counts)
        }
    
    def _start_auto_flush_timer(self):
        """Start auto-flush timer for periodic log flushing"""
        def flush_logs():
            while True:
                time.sleep(self.auto_flush_interval)
                try:
                    for handler in self.logger.handlers:
                        handler.flush()
                except Exception as e:
                    print(f"Error flushing logs: {e}")
        
        flush_thread = threading.Thread(target=flush_logs, daemon=True)
        flush_thread.start()
    
    def flush_all(self):
        """Flush all log handlers"""
        for handler in self.logger.handlers:
            handler.flush()
    
    def close(self):
        """Close all log handlers"""
        for handler in self.logger.handlers:
            handler.close()
            self.logger.removeHandler(handler)
