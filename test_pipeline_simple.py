#!/usr/bin/env python3
"""
Simple test script for the HL7 pipeline components
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def test_enhanced_validator():
    """Test the enhanced validator"""
    print("Testing Enhanced Validator...")
    
    try:
        from enhanced_validator import EnhancedHL7Validator
        
        validator = EnhancedHL7Validator()
        
        # Test with a simple valid HL7 message
        test_message = """MSH|^~\\&|TestApp|TestFacility|ReceivingApp|ReceivingFacility|20240902082341||ADT^A04^ADT_A04|MSG001|P|2.8
EVN|A04|20240902082341||||
PID|1|12345^^^MRN^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345^USA||555-1234|||M"""
        
        result = validator.validate_message(test_message)
        
        print(f"  ✅ Validator created successfully")
        print(f"  ✅ Message validation result: Valid={result.is_valid}, Issues={len(result.issues)}")
        
        if result.issues:
            print("  Issues found:")
            for issue in result.issues[:3]:  # Show first 3 issues
                print(f"    - {issue.severity.value}: {issue.code} - {issue.message}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing validator: {e}")
        return False

def test_enhancement_optimizer():
    """Test the enhancement optimizer"""
    print("Testing Enhancement Optimizer...")
    
    try:
        from enhanced_validator import EnhancedHL7Validator
        from enhancement_optimizer import EnhancementOptimizer
        
        validator = EnhancedHL7Validator()
        optimizer = EnhancementOptimizer(validator)
        
        # Test with a message that needs fixing (wrong encoding chars)
        test_message = """MSH|^~&a|TestApp|TestFacility|ReceivingApp|ReceivingFacility|20240902082341||ADT^A04^ADT_A04|MSG001|P|2.8
PID|1|12345^^^MRN^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345^USA||555-1234|||M"""
        
        result = optimizer.enhance_message(test_message)
        
        print(f"  ✅ Optimizer created successfully")
        print(f"  ✅ Enhancement result: Success={result.success}, Actions={len(result.actions_performed)}")
        
        if result.actions_performed:
            print("  Actions performed:")
            for action in result.actions_performed[:3]:  # Show first 3 actions
                print(f"    - {action.action_type}: {action.description}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing optimizer: {e}")
        return False

def test_quarantine_manager():
    """Test the quarantine manager"""
    print("Testing Quarantine Manager...")
    
    try:
        from quarantine_manager import QuarantineManager
        from enhanced_validator import ValidationResult, ValidationIssue, ValidationSeverity
        
        # Create temporary quarantine directory
        quarantine_dir = "test_quarantine"
        Path(quarantine_dir).mkdir(exist_ok=True)
        
        manager = QuarantineManager(quarantine_dir)
        
        # Create a mock validation result
        mock_issue = ValidationIssue(
            severity=ValidationSeverity.ERROR,
            code="TEST_ERROR",
            message="Test error message"
        )
        
        mock_validation = ValidationResult(
            is_valid=False,
            message_type="ADT",
            version="2.8",
            issues=[mock_issue],
            statistics={}
        )
        
        print(f"  ✅ Quarantine manager created successfully")
        
        # Get stats (should be empty initially)
        stats = manager.get_quarantine_stats()
        print(f"  ✅ Stats retrieved: {stats.total_quarantined} quarantined messages")
        
        # Clean up
        import shutil
        if Path(quarantine_dir).exists():
            shutil.rmtree(quarantine_dir)
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing quarantine manager: {e}")
        return False

def test_existing_hl7_files():
    """Test with existing HL7 files"""
    print("Testing with existing HL7 files...")
    
    try:
        from enhanced_validator import EnhancedHL7Validator
        
        validator = EnhancedHL7Validator()
        
        # Look for HL7 files in the enhancedHl7 directory
        hl7_dir = Path("enhancedHl7")
        if not hl7_dir.exists():
            print("  ⚠️  No enhancedHl7 directory found")
            return True
        
        hl7_files = list(hl7_dir.glob("**/*.hl7"))
        if not hl7_files:
            print("  ⚠️  No HL7 files found in enhancedHl7 directory")
            return True
        
        print(f"  📁 Found {len(hl7_files)} HL7 files")
        
        # Test first few files
        valid_count = 0
        invalid_count = 0
        
        for i, file_path in enumerate(hl7_files[:5]):  # Test first 5 files
            try:
                result = validator.validate_file(str(file_path))
                if result.is_valid:
                    valid_count += 1
                else:
                    invalid_count += 1
                    
            except Exception as e:
                print(f"    ❌ Error validating {file_path.name}: {e}")
                invalid_count += 1
        
        print(f"  ✅ Tested {valid_count + invalid_count} files: {valid_count} valid, {invalid_count} invalid")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing existing files: {e}")
        return False

def main():
    """Main test function"""
    print("="*60)
    print("HL7 PIPELINE COMPONENT TESTING")
    print("="*60)
    print()
    
    tests = [
        ("Enhanced Validator", test_enhanced_validator),
        ("Enhancement Optimizer", test_enhancement_optimizer),
        ("Quarantine Manager", test_quarantine_manager),
        ("Existing HL7 Files", test_existing_hl7_files)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"🧪 {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"  ✅ PASSED")
            else:
                failed += 1
                print(f"  ❌ FAILED")
        except Exception as e:
            failed += 1
            print(f"  ❌ FAILED: {e}")
        print()
    
    print("="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        print()
        print("🎉 All tests passed! The pipeline components are working correctly.")
        print("You can now run the full pipeline with confidence.")
    else:
        print()
        print("⚠️  Some tests failed. Please review the errors above.")
    
    return 0 if failed == 0 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
