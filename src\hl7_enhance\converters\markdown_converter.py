"""
HL7 to Markdown converter

Converts HL7 files to Markdown format for documentation and review purposes.
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Optional, Set
from dataclasses import dataclass


@dataclass
class ConversionConfig:
    """Configuration for HL7 to Markdown conversion"""
    input_folder: str = "data/input"
    output_folder: str = "data/output"
    messages_per_file: int = 25
    output_filename_base: str = "compiled_hl7_messages_part"
    remove_duplicates: bool = True
    include_source_info: bool = True


@dataclass
class ConversionResult:
    """Result of HL7 to Markdown conversion"""
    total_files_processed: int
    unique_messages_found: int
    markdown_files_created: int
    duplicates_removed: int
    errors_encountered: List[str]


class HL7ToMarkdownConverter:
    """Converts HL7 files to Markdown format for documentation"""
    
    def __init__(self, config: Optional[ConversionConfig] = None):
        """Initialize the converter with configuration"""
        self.config = config or ConversionConfig()
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_file = Path("logs/hl7_conversion.log")
        log_file.parent.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, mode='w'),
                logging.StreamHandler()
            ]
        )
    
    def validate_paths(self) -> bool:
        """Validate input and output paths"""
        input_path = Path(self.config.input_folder)
        
        if not input_path.exists():
            self.logger.error(f"Input folder does not exist: {self.config.input_folder}")
            return False
        
        # Create output directory if it doesn't exist
        try:
            output_path = Path(self.config.output_folder)
            output_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Output directory ready: {self.config.output_folder}")
            return True
        except OSError as e:
            self.logger.error(f"Could not create output directory: {e}")
            return False
    
    def is_hl7_file(self, file_path: Path) -> bool:
        """Check if file is likely an HL7 file"""
        # Check file extension
        if file_path.suffix.lower() in ['.hl7', '.txt', '.msg', '.dat']:
            return True
        
        # Check content for MSH segment
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                first_line = f.readline().strip()
                return first_line.startswith('MSH')
        except Exception:
            return False
    
    def read_hl7_file(self, file_path: Path) -> Optional[str]:
        """Read and validate HL7 file content"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()
            
            if not content:
                self.logger.warning(f"Empty file: {file_path}")
                return None
            
            # Basic HL7 validation
            if not content.startswith('MSH'):
                self.logger.warning(f"File does not appear to be HL7: {file_path}")
                return None
            
            return content
        
        except Exception as e:
            self.logger.error(f"Error reading file {file_path}: {e}")
            return None
    
    def collect_unique_messages(self) -> List[Dict[str, str]]:
        """Collect unique HL7 messages from input folder"""
        unique_messages = []
        seen_content: Set[str] = set()
        input_path = Path(self.config.input_folder)
        
        self.logger.info(f"Scanning for HL7 files in: {input_path}")
        
        # Find all potential HL7 files
        hl7_files = []
        for file_path in input_path.rglob('*'):
            if file_path.is_file() and self.is_hl7_file(file_path):
                hl7_files.append(file_path)
        
        self.logger.info(f"Found {len(hl7_files)} potential HL7 files")
        
        for file_path in sorted(hl7_files):
            content = self.read_hl7_file(file_path)
            
            if content is None:
                continue
            
            # Check for duplicates if enabled
            if self.config.remove_duplicates:
                if content in seen_content:
                    self.logger.debug(f"Skipping duplicate: {file_path}")
                    continue
                seen_content.add(content)
            
            unique_messages.append({
                'content': content,
                'filename': file_path.name,
                'relative_path': str(file_path.relative_to(input_path))
            })
            self.logger.debug(f"Collected message from: {file_path}")
        
        self.logger.info(f"Collected {len(unique_messages)} unique messages")
        return unique_messages
    
    def format_message_to_markdown(self, message_data: Dict[str, str], message_number: int) -> str:
        """Format a single HL7 message to Markdown"""
        markdown_parts = []
        
        # Message header
        markdown_parts.append(f"## Message {message_number}\n")
        
        # Source information if enabled
        if self.config.include_source_info:
            markdown_parts.append(f"**Source File:** `{message_data['filename']}`\n")
            if 'relative_path' in message_data:
                markdown_parts.append(f"**Path:** `{message_data['relative_path']}`\n")
        
        # HL7 content in code block
        markdown_parts.append("```hl7\n")
        
        # Format HL7 content with proper line breaks
        content = message_data['content']
        # Convert \r to \n for better markdown display
        formatted_content = content.replace('\r', '\n')
        markdown_parts.append(formatted_content)
        
        markdown_parts.append("\n```\n\n---\n\n")
        
        return "".join(markdown_parts)
    
    def create_markdown_files(self, messages: List[Dict[str, str]]) -> int:
        """Create batched Markdown files from messages"""
        if not messages:
            self.logger.warning("No messages to convert")
            return 0
        
        files_created = 0
        output_path = Path(self.config.output_folder)
        
        for i in range(0, len(messages), self.config.messages_per_file):
            batch = messages[i:i + self.config.messages_per_file]
            file_number = (i // self.config.messages_per_file) + 1
            
            # Generate markdown content for this batch
            markdown_parts = [f"# Compiled HL7 Messages (Part {file_number})\n\n"]
            
            for msg_index, message_data in enumerate(batch):
                message_number = msg_index + 1
                markdown_content = self.format_message_to_markdown(message_data, message_number)
                markdown_parts.append(markdown_content)
            
            # Write to file
            output_filename = f"{self.config.output_filename_base}_{file_number}.md"
            output_filepath = output_path / output_filename
            
            try:
                with open(output_filepath, 'w', encoding='utf-8') as f:
                    f.write("".join(markdown_parts))
                
                self.logger.info(f"Created: {output_filename} with {len(batch)} messages")
                files_created += 1
                
            except Exception as e:
                self.logger.error(f"Failed to write {output_filename}: {e}")
        
        return files_created

    def convert_batch(self) -> ConversionResult:
        """Perform batch conversion of HL7 files to Markdown"""
        self.logger.info("Starting HL7 to Markdown batch conversion")

        errors = []

        # Validate paths
        if not self.validate_paths():
            return ConversionResult(
                total_files_processed=0,
                unique_messages_found=0,
                markdown_files_created=0,
                duplicates_removed=0,
                errors_encountered=["Path validation failed"]
            )

        try:
            # Collect unique messages
            messages = self.collect_unique_messages()

            if not messages:
                self.logger.warning("No HL7 messages found to convert")
                return ConversionResult(
                    total_files_processed=0,
                    unique_messages_found=0,
                    markdown_files_created=0,
                    duplicates_removed=0,
                    errors_encountered=["No HL7 messages found"]
                )

            # Create markdown files
            files_created = self.create_markdown_files(messages)

            # Calculate statistics
            input_path = Path(self.config.input_folder)
            total_files = len(list(input_path.rglob('*')))
            duplicates_removed = total_files - len(messages) if self.config.remove_duplicates else 0

            result = ConversionResult(
                total_files_processed=total_files,
                unique_messages_found=len(messages),
                markdown_files_created=files_created,
                duplicates_removed=duplicates_removed,
                errors_encountered=errors
            )

            self.logger.info(f"Conversion completed: {files_created} files created")
            return result

        except Exception as e:
            error_msg = f"Conversion failed: {e}"
            self.logger.error(error_msg)
            errors.append(error_msg)

            return ConversionResult(
                total_files_processed=0,
                unique_messages_found=0,
                markdown_files_created=0,
                duplicates_removed=0,
                errors_encountered=errors
            )

    def convert_single_file(self, input_file: str, output_file: str) -> bool:
        """Convert a single HL7 file to Markdown"""
        try:
            input_path = Path(input_file)
            output_path = Path(output_file)

            # Read HL7 content
            content = self.read_hl7_file(input_path)
            if content is None:
                return False

            # Create message data
            message_data = {
                'content': content,
                'filename': input_path.name,
                'relative_path': str(input_path)
            }

            # Format to markdown
            markdown_content = f"# HL7 Message: {input_path.name}\n\n"
            markdown_content += self.format_message_to_markdown(message_data, 1)

            # Write to output file
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            self.logger.info(f"Converted {input_file} to {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to convert {input_file}: {e}")
            return False
