"""
HL7 Enhancement Python Tool

A comprehensive Python-based HL7 message processing and enhancement engine 
designed for healthcare data standardization and validation.
"""

__version__ = "1.0.0"
__author__ = "M.F.M Fazrin"
__email__ = "<EMAIL>"

# Import main classes for easy access
from .core.processor import HL7Processor
from .validators.validator import HL7Validator
from .converters.markdown_converter import HL7ToMarkdownConverter
from .pipeline.orchestrator import PipelineOrchestrator

__all__ = [
    "HL7Processor",
    "HL7Validator", 
    "HL7ToMarkdownConverter",
    "PipelineOrchestrator",
]
