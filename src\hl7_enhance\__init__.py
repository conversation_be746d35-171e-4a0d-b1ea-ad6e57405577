"""
HL7 Enhancement Python Tool

A comprehensive Python-based HL7 message processing and enhancement engine 
designed for healthcare data standardization and validation.
"""

__version__ = "1.0.0"
__author__ = "M.F.M Fazrin"
__email__ = "<EMAIL>"

# Import main classes for easy access
try:
    from .core.processor import HL7Processor
    from .validators.validator import HL7Validator
    from .validators.enhanced_validator import EnhancedHL7Validator
    from .converters.markdown_converter import HL7ToMarkdownConverter
    from .pipeline.orchestrator import PipelineOrchestrator
    from .pipeline.quarantine import QuarantineManager
    from .pipeline.logger import PipelineLogger

    __all__ = [
        "HL7Processor",
        "HL7Validator",
        "EnhancedHL7Validator",
        "HL7ToMarkdownConverter",
        "PipelineOrchestrator",
        "QuarantineManager",
        "PipelineLogger",
    ]
except ImportError as e:
    # <PERSON>le missing dependencies gracefully
    print(f"Warning: Some modules could not be imported: {e}")
    __all__ = []
