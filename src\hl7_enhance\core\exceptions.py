"""
Custom exceptions for HL7 processing
"""


class HL7ProcessingError(Exception):
    """Custom exception for HL7 processing errors"""
    
    def __init__(self, message: str, error_code: str, file_path: str = None):
        self.message = message
        self.error_code = error_code
        self.file_path = file_path
        super().__init__(self.message)


class HL7ValidationError(Exception):
    """Exception raised when HL7 validation fails"""
    
    def __init__(self, message: str, validation_errors: list = None):
        self.message = message
        self.validation_errors = validation_errors or []
        super().__init__(self.message)


class HL7ConfigurationError(Exception):
    """Exception raised when configuration is invalid"""
    
    def __init__(self, message: str, config_file: str = None):
        self.message = message
        self.config_file = config_file
        super().__init__(self.message)


class HL7QuarantineError(Exception):
    """Exception raised during quarantine operations"""
    
    def __init__(self, message: str, file_path: str = None):
        self.message = message
        self.file_path = file_path
        super().__init__(self.message)
