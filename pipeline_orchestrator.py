#!/usr/bin/env python3
"""
Pipeline Orchestrator for Automated HL7 Validation and Enhancement
Coordinates validation, enhancement, optimization, and quarantine processes
"""

import logging
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import glob

from enhanced_validator import EnhancedHL7Validator, ValidationResult
from enhancement_optimizer import EnhancementOptimizer, EnhancementResult
from quarantine_manager import QuarantineManager, QuarantineRecord

@dataclass
class PipelineStats:
    """Statistics for pipeline processing"""
    total_files_processed: int = 0
    files_successfully_enhanced: int = 0
    files_quarantined: int = 0
    files_already_valid: int = 0
    total_enhancement_actions: int = 0
    processing_time_seconds: float = 0.0
    validation_time_seconds: float = 0.0
    enhancement_time_seconds: float = 0.0
    quarantine_time_seconds: float = 0.0

@dataclass
class PipelineConfig:
    """Configuration for the pipeline"""
    source_directory: str
    output_directory: str
    quarantine_directory: str
    file_patterns: List[str] = None
    max_enhancement_retries: int = 3
    enable_auto_retry: bool = True
    batch_size: int = 100
    parallel_processing: bool = False
    backup_original_files: bool = True
    generate_reports: bool = True
    log_level: str = "INFO"

class PipelineOrchestrator:
    """Main orchestrator for the HL7 validation and enhancement pipeline"""
    
    def __init__(self, config: PipelineConfig):
        """Initialize the pipeline orchestrator"""
        self.config = config
        self.logger = self._setup_logging()
        
        # Initialize components
        self.validator = EnhancedHL7Validator()
        self.optimizer = EnhancementOptimizer(self.validator)
        self.quarantine_manager = QuarantineManager(
            config.quarantine_directory,
            {
                'max_retry_attempts': config.max_enhancement_retries,
                'auto_retry_enabled': config.enable_auto_retry
            }
        )
        
        # Create directories
        self.source_dir = Path(config.source_directory)
        self.output_dir = Path(config.output_directory)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Statistics
        self.stats = PipelineStats()
        self.processing_results = []
        
        # File patterns
        self.file_patterns = config.file_patterns or ["*.hl7", "*.txt"]
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, self.config.log_level.upper()))
        
        # Create console handler
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def run_pipeline(self) -> Dict[str, Any]:
        """Run the complete validation and enhancement pipeline"""
        start_time = time.time()
        
        self.logger.info("Starting HL7 Validation and Enhancement Pipeline")
        self.logger.info(f"Source directory: {self.config.source_directory}")
        self.logger.info(f"Output directory: {self.config.output_directory}")
        self.logger.info(f"Quarantine directory: {self.config.quarantine_directory}")
        
        try:
            # Step 1: Discover HL7 files
            files_to_process = self._discover_files()
            self.logger.info(f"Discovered {len(files_to_process)} files to process")
            
            if not files_to_process:
                self.logger.warning("No HL7 files found to process")
                return self._generate_final_report()
            
            # Step 2: Process retry-eligible quarantined messages first
            self._process_retry_eligible_messages()
            
            # Step 3: Process discovered files in batches
            self._process_files_in_batches(files_to_process)
            
            # Step 4: Generate comprehensive reports
            if self.config.generate_reports:
                self._generate_reports()
            
            # Calculate total processing time
            self.stats.processing_time_seconds = time.time() - start_time
            
            self.logger.info("Pipeline processing completed successfully")
            return self._generate_final_report()
            
        except Exception as e:
            self.logger.error(f"Pipeline processing failed: {e}")
            raise
    
    def _discover_files(self) -> List[Path]:
        """Discover HL7 files to process"""
        files = []
        
        for pattern in self.file_patterns:
            search_pattern = str(self.source_dir / "**" / pattern)
            found_files = glob.glob(search_pattern, recursive=True)
            files.extend([Path(f) for f in found_files])
        
        # Remove duplicates and filter out already processed files
        unique_files = list(set(files))
        
        # Filter out files that are already in quarantine
        quarantine_files = set()
        for category_dir in (Path(self.config.quarantine_directory) / "categories").glob("*"):
            if category_dir.is_dir():
                quarantine_files.update(category_dir.glob("*"))
        
        filtered_files = [f for f in unique_files if f not in quarantine_files]
        
        return sorted(filtered_files)
    
    def _process_retry_eligible_messages(self):
        """Process messages eligible for retry from quarantine"""
        retry_eligible = self.quarantine_manager.get_retry_eligible_messages()
        
        if retry_eligible:
            self.logger.info(f"Processing {len(retry_eligible)} retry-eligible quarantined messages")
            
            for record in retry_eligible:
                try:
                    file_path = record.file_path
                    if Path(file_path).exists():
                        result = self._process_single_file(file_path, is_retry=True)
                        
                        # Update retry information
                        self.quarantine_manager.update_retry_attempt(
                            record, 
                            success=result.get('success', False)
                        )
                        
                except Exception as e:
                    self.logger.error(f"Error processing retry message {record.file_path}: {e}")
    
    def _process_files_in_batches(self, files: List[Path]):
        """Process files in configurable batches"""
        total_files = len(files)
        batch_size = self.config.batch_size
        
        for i in range(0, total_files, batch_size):
            batch = files[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (total_files + batch_size - 1) // batch_size
            
            self.logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} files)")
            
            for file_path in batch:
                try:
                    result = self._process_single_file(file_path)
                    self.processing_results.append(result)
                    
                    # Update statistics
                    self.stats.total_files_processed += 1
                    
                    if result['success']:
                        if result['was_enhanced']:
                            self.stats.files_successfully_enhanced += 1
                            self.stats.total_enhancement_actions += result.get('enhancement_actions', 0)
                        else:
                            self.stats.files_already_valid += 1
                    else:
                        self.stats.files_quarantined += 1
                    
                    # Progress reporting
                    if self.stats.total_files_processed % 50 == 0:
                        progress = (self.stats.total_files_processed / total_files) * 100
                        self.logger.info(f"Progress: {self.stats.total_files_processed}/{total_files} ({progress:.1f}%)")
                        
                except Exception as e:
                    self.logger.error(f"Error processing file {file_path}: {e}")
                    self.stats.total_files_processed += 1
                    self.stats.files_quarantined += 1
    
    def _process_single_file(self, file_path: str, is_retry: bool = False) -> Dict[str, Any]:
        """Process a single HL7 file through the validation and enhancement pipeline"""
        file_path = str(file_path)
        start_time = time.time()
        
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if not content.strip():
                return self._handle_empty_file(file_path)
            
            # Step 1: Initial validation
            validation_start = time.time()
            validation_result = self.validator.validate_message(content, file_path)
            self.stats.validation_time_seconds += time.time() - validation_start
            
            # If already valid, save to output and return
            if validation_result.is_valid:
                self._save_valid_message(file_path, content)
                return {
                    'file_path': file_path,
                    'success': True,
                    'was_enhanced': False,
                    'validation_result': validation_result,
                    'processing_time': time.time() - start_time
                }
            
            # Step 2: Attempt enhancement
            enhancement_start = time.time()
            enhancement_result = self.optimizer.enhance_message(content, file_path)
            self.stats.enhancement_time_seconds += time.time() - enhancement_start
            
            # Step 3: Handle enhancement result
            if enhancement_result.success:
                # Save enhanced message
                self._save_enhanced_message(file_path, enhancement_result.enhanced_content)
                
                return {
                    'file_path': file_path,
                    'success': True,
                    'was_enhanced': True,
                    'validation_result': validation_result,
                    'enhancement_result': enhancement_result,
                    'enhancement_actions': len(enhancement_result.actions_performed),
                    'processing_time': time.time() - start_time
                }
            else:
                # Step 4: Quarantine failed enhancement
                quarantine_start = time.time()
                quarantine_record = self.quarantine_manager.quarantine_message(
                    file_path,
                    enhancement_result.validation_after or validation_result,
                    enhancement_result.retry_count,
                    [action.error_message for action in enhancement_result.actions_performed 
                     if not action.success]
                )
                self.stats.quarantine_time_seconds += time.time() - quarantine_start
                
                return {
                    'file_path': file_path,
                    'success': False,
                    'was_enhanced': False,
                    'validation_result': validation_result,
                    'enhancement_result': enhancement_result,
                    'quarantine_record': quarantine_record,
                    'processing_time': time.time() - start_time
                }
                
        except Exception as e:
            self.logger.error(f"Unexpected error processing {file_path}: {e}")
            return {
                'file_path': file_path,
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def _handle_empty_file(self, file_path: str) -> Dict[str, Any]:
        """Handle empty file case"""
        from enhanced_validator import ValidationResult, ValidationIssue, ValidationSeverity
        
        empty_validation = ValidationResult(
            is_valid=False,
            message_type="UNKNOWN",
            version="UNKNOWN",
            issues=[ValidationIssue(
                severity=ValidationSeverity.ERROR,
                code="EMPTY_FILE",
                message="File is empty",
                fix_suggestion="Provide valid HL7 message content"
            )],
            statistics={'file_path': file_path}
        )
        
        quarantine_record = self.quarantine_manager.quarantine_message(
            file_path, empty_validation, 0
        )
        
        return {
            'file_path': file_path,
            'success': False,
            'was_enhanced': False,
            'validation_result': empty_validation,
            'quarantine_record': quarantine_record,
            'processing_time': 0.0
        }
    
    def _save_valid_message(self, file_path: str, content: str):
        """Save already valid message to output directory"""
        relative_path = Path(file_path).relative_to(self.source_dir)
        output_path = self.output_dir / relative_path
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _save_enhanced_message(self, file_path: str, enhanced_content: str):
        """Save enhanced message to output directory"""
        relative_path = Path(file_path).relative_to(self.source_dir)
        output_path = self.output_dir / relative_path
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Backup original if configured
        if self.config.backup_original_files:
            backup_path = output_path.with_suffix(output_path.suffix + '.original')
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
        
        # Save enhanced content
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_content)

    def _generate_reports(self):
        """Generate comprehensive pipeline reports"""
        reports_dir = Path(self.config.output_directory) / "reports"
        reports_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Generate pipeline summary report
        pipeline_report = self._generate_pipeline_report()
        pipeline_report_path = reports_dir / f"pipeline_report_{timestamp}.json"
        with open(pipeline_report_path, 'w') as f:
            json.dump(pipeline_report, f, indent=2, default=str)

        # Generate quarantine report
        quarantine_report = self.quarantine_manager.generate_quarantine_report()
        quarantine_report_path = reports_dir / f"quarantine_report_{timestamp}.json"
        with open(quarantine_report_path, 'w') as f:
            json.dump(quarantine_report, f, indent=2, default=str)

        # Generate CSV summary
        csv_report_path = reports_dir / f"pipeline_summary_{timestamp}.csv"
        self._generate_csv_summary(csv_report_path)

        self.logger.info(f"Reports generated in {reports_dir}")

    def _generate_pipeline_report(self) -> Dict[str, Any]:
        """Generate comprehensive pipeline report"""
        return {
            'pipeline_execution': {
                'timestamp': datetime.now().isoformat(),
                'configuration': asdict(self.config),
                'statistics': asdict(self.stats)
            },
            'processing_summary': {
                'total_files': self.stats.total_files_processed,
                'success_rate': (self.stats.files_successfully_enhanced + self.stats.files_already_valid) / max(self.stats.total_files_processed, 1) * 100,
                'enhancement_rate': self.stats.files_successfully_enhanced / max(self.stats.total_files_processed, 1) * 100,
                'quarantine_rate': self.stats.files_quarantined / max(self.stats.total_files_processed, 1) * 100
            },
            'performance_metrics': {
                'total_processing_time': self.stats.processing_time_seconds,
                'average_file_processing_time': self.stats.processing_time_seconds / max(self.stats.total_files_processed, 1),
                'validation_time_percentage': (self.stats.validation_time_seconds / max(self.stats.processing_time_seconds, 1)) * 100,
                'enhancement_time_percentage': (self.stats.enhancement_time_seconds / max(self.stats.processing_time_seconds, 1)) * 100,
                'quarantine_time_percentage': (self.stats.quarantine_time_seconds / max(self.stats.processing_time_seconds, 1)) * 100
            },
            'detailed_results': self.processing_results
        }

    def _generate_csv_summary(self, output_path: Path):
        """Generate CSV summary report"""
        import csv

        with open(output_path, 'w', newline='') as f:
            writer = csv.writer(f)

            # Write summary statistics
            writer.writerow(['Pipeline Summary Report'])
            writer.writerow(['Generated:', datetime.now().isoformat()])
            writer.writerow([])

            writer.writerow(['Metric', 'Value'])
            writer.writerow(['Total Files Processed', self.stats.total_files_processed])
            writer.writerow(['Files Successfully Enhanced', self.stats.files_successfully_enhanced])
            writer.writerow(['Files Already Valid', self.stats.files_already_valid])
            writer.writerow(['Files Quarantined', self.stats.files_quarantined])
            writer.writerow(['Total Enhancement Actions', self.stats.total_enhancement_actions])
            writer.writerow(['Processing Time (seconds)', f"{self.stats.processing_time_seconds:.2f}"])

            success_rate = (self.stats.files_successfully_enhanced + self.stats.files_already_valid) / max(self.stats.total_files_processed, 1) * 100
            writer.writerow(['Success Rate (%)', f"{success_rate:.2f}"])

            enhancement_rate = self.stats.files_successfully_enhanced / max(self.stats.total_files_processed, 1) * 100
            writer.writerow(['Enhancement Rate (%)', f"{enhancement_rate:.2f}"])

            quarantine_rate = self.stats.files_quarantined / max(self.stats.total_files_processed, 1) * 100
            writer.writerow(['Quarantine Rate (%)', f"{quarantine_rate:.2f}"])

    def _generate_final_report(self) -> Dict[str, Any]:
        """Generate final pipeline execution report"""
        return {
            'execution_summary': {
                'status': 'completed',
                'timestamp': datetime.now().isoformat(),
                'total_files_processed': self.stats.total_files_processed,
                'files_successfully_enhanced': self.stats.files_successfully_enhanced,
                'files_already_valid': self.stats.files_already_valid,
                'files_quarantined': self.stats.files_quarantined,
                'processing_time_seconds': self.stats.processing_time_seconds
            },
            'performance_metrics': {
                'average_processing_time_per_file': self.stats.processing_time_seconds / max(self.stats.total_files_processed, 1),
                'files_per_second': self.stats.total_files_processed / max(self.stats.processing_time_seconds, 1),
                'success_rate_percentage': (self.stats.files_successfully_enhanced + self.stats.files_already_valid) / max(self.stats.total_files_processed, 1) * 100
            },
            'quarantine_summary': self.quarantine_manager.get_quarantine_stats(),
            'recommendations': self._generate_pipeline_recommendations()
        }

    def _generate_pipeline_recommendations(self) -> List[str]:
        """Generate recommendations based on pipeline results"""
        recommendations = []

        if self.stats.total_files_processed == 0:
            recommendations.append("No files were processed. Check source directory and file patterns.")
            return recommendations

        quarantine_rate = self.stats.files_quarantined / self.stats.total_files_processed

        if quarantine_rate > 0.1:  # More than 10% quarantined
            recommendations.append("High quarantine rate detected. Review source data quality and enhancement strategies.")

        if quarantine_rate > 0.2:  # More than 20% quarantined
            recommendations.append("Very high quarantine rate. Consider implementing pre-processing validation at source systems.")

        enhancement_rate = self.stats.files_successfully_enhanced / self.stats.total_files_processed

        if enhancement_rate > 0.5:  # More than 50% needed enhancement
            recommendations.append("High enhancement rate indicates systematic issues in source data. Review data generation processes.")

        if self.stats.total_enhancement_actions > 0:
            avg_actions_per_file = self.stats.total_enhancement_actions / self.stats.files_successfully_enhanced
            if avg_actions_per_file > 3:
                recommendations.append("High number of enhancement actions per file. Consider improving source data quality.")

        if self.stats.processing_time_seconds > 0:
            files_per_second = self.stats.total_files_processed / self.stats.processing_time_seconds
            if files_per_second < 1:
                recommendations.append("Low processing throughput. Consider optimizing validation and enhancement algorithms.")

        return recommendations

def main():
    """Main entry point for the pipeline orchestrator"""
    import argparse

    parser = argparse.ArgumentParser(description="HL7 Validation and Enhancement Pipeline")
    parser.add_argument("--source", required=True, help="Source directory containing HL7 files")
    parser.add_argument("--output", required=True, help="Output directory for enhanced files")
    parser.add_argument("--quarantine", required=True, help="Quarantine directory for problematic files")
    parser.add_argument("--batch-size", type=int, default=100, help="Batch size for processing")
    parser.add_argument("--max-retries", type=int, default=3, help="Maximum enhancement retry attempts")
    parser.add_argument("--log-level", default="INFO", help="Logging level")
    parser.add_argument("--file-patterns", nargs="+", default=["*.hl7"], help="File patterns to process")

    args = parser.parse_args()

    config = PipelineConfig(
        source_directory=args.source,
        output_directory=args.output,
        quarantine_directory=args.quarantine,
        batch_size=args.batch_size,
        max_enhancement_retries=args.max_retries,
        log_level=args.log_level,
        file_patterns=args.file_patterns
    )

    orchestrator = PipelineOrchestrator(config)
    result = orchestrator.run_pipeline()

    print("\n" + "="*60)
    print("PIPELINE EXECUTION SUMMARY")
    print("="*60)
    print(f"Total files processed: {result['execution_summary']['total_files_processed']}")
    print(f"Files successfully enhanced: {result['execution_summary']['files_successfully_enhanced']}")
    print(f"Files already valid: {result['execution_summary']['files_already_valid']}")
    print(f"Files quarantined: {result['execution_summary']['files_quarantined']}")
    print(f"Success rate: {result['performance_metrics']['success_rate_percentage']:.2f}%")
    print(f"Processing time: {result['execution_summary']['processing_time_seconds']:.2f} seconds")

    if result['recommendations']:
        print("\nRECOMMENDATIONS:")
        for i, rec in enumerate(result['recommendations'], 1):
            print(f"{i}. {rec}")

if __name__ == "__main__":
    main()
