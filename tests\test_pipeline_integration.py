#!/usr/bin/env python3
"""
Integration tests for the HL7 Validation and Enhancement Pipeline
Tests the complete pipeline functionality including edge cases and performance
"""

import unittest
import tempfile
import shutil
import json
import time
from pathlib import Path
from unittest.mock import patch, MagicMock

# Import pipeline components
import sys
sys.path.append('..')

from enhanced_validator import EnhancedHL7Validator, ValidationSeverity
from enhancement_optimizer import EnhancementOptimizer
from quarantine_manager import QuarantineManager, QuarantineCategory
from pipeline_orchestrator import PipelineOrchestrator, PipelineConfig
from pipeline_logger import PipelineLogger, EventType, LogLevel

class TestPipelineIntegration(unittest.TestCase):
    """Integration tests for the complete HL7 pipeline"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.source_dir = self.test_dir / "source"
        self.output_dir = self.test_dir / "output"
        self.quarantine_dir = self.test_dir / "quarantine"
        self.log_dir = self.test_dir / "logs"
        
        # Create directories
        for dir_path in [self.source_dir, self.output_dir, self.quarantine_dir, self.log_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Create test HL7 messages
        self.create_test_messages()
        
        # Initialize pipeline components
        self.validator = EnhancedHL7Validator()
        self.optimizer = EnhancementOptimizer(self.validator)
        self.quarantine_manager = QuarantineManager(str(self.quarantine_dir))
        self.logger = PipelineLogger(str(self.log_dir))
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir)
    
    def create_test_messages(self):
        """Create test HL7 messages with various issues"""
        
        # Valid HL7 message
        valid_message = """MSH|^~\\&|TestApp|TestFacility|ReceivingApp|ReceivingFacility|20240902082341||ADT^A04^ADT_A04|MSG001|P|2.8
EVN|A04|20240902082341||||
PID|1|12345^^^MRN^MRN||DOE^JOHN^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345^USA||555-1234|||M
PV1|1|I|ICU^101^1|||DOC123^SMITH^JANE|||||||||||VIP001||||||||||||||||||||20240902082341"""
        
        # Message with encoding character issues
        encoding_issue_message = """MSH|^~&a|TestApp|TestFacility|ReceivingApp|ReceivingFacility|20240902082341||ADT^A04^ADT_A04|MSG002|P|2.8
PID|1|12345^^^MRN^MRN||DOE^JANE^MIDDLE||19850101|F|||456 OAK ST^^ANYTOWN^ST^12345^USA||555-5678|||F"""
        
        # Message missing EVN segment
        missing_evn_message = """MSH|^~\\&|TestApp|TestFacility|ReceivingApp|ReceivingFacility|20240902082341||ADT^A04^ADT_A04|MSG003|P|2.8
PID|1|12345^^^MRN^MRN||DOE^BOB^MIDDLE||19900101|M|||789 ELM ST^^ANYTOWN^ST^12345^USA||555-9012|||M"""
        
        # Message with wrong version
        wrong_version_message = """MSH|^~\\&|TestApp|TestFacility|ReceivingApp|ReceivingFacility|20240902082341||ADT^A04^ADT_A04|MSG004|P|2.5
EVN|A04|20240902082341||||
PID|1|12345^^^MRN^MRN||DOE^ALICE^MIDDLE||19750101|F|||321 PINE ST^^ANYTOWN^ST^12345^USA||555-3456|||F"""
        
        # Empty file
        empty_message = ""
        
        # Malformed message
        malformed_message = "This is not a valid HL7 message"
        
        # Complex message needing multiple fixes
        complex_issue_message = """MSH|^~&x|BadApp|TestFacility|ReceivingApp|ReceivingFacility|20240902082341||ADT^A04^ADT_A04|MSG005|T|2.7
PID|1||||DOE^CHARLIE^MIDDLE||19950101|M|||654 MAPLE ST^^ANYTOWN^ST^12345^USA||555-7890|||M"""
        
        # Save test messages
        test_messages = {
            "valid_message.hl7": valid_message,
            "encoding_issue.hl7": encoding_issue_message,
            "missing_evn.hl7": missing_evn_message,
            "wrong_version.hl7": wrong_version_message,
            "empty_file.hl7": empty_message,
            "malformed.hl7": malformed_message,
            "complex_issues.hl7": complex_issue_message
        }
        
        for filename, content in test_messages.items():
            with open(self.source_dir / filename, 'w') as f:
                f.write(content)
    
    def test_enhanced_validator_comprehensive(self):
        """Test enhanced validator with various message types"""
        
        # Test valid message
        valid_file = self.source_dir / "valid_message.hl7"
        result = self.validator.validate_file(str(valid_file))
        self.assertTrue(result.is_valid)
        self.assertEqual(result.message_type, "ADT")
        self.assertEqual(result.version, "2.8")
        
        # Test encoding issue
        encoding_file = self.source_dir / "encoding_issue.hl7"
        result = self.validator.validate_file(str(encoding_file))
        self.assertFalse(result.is_valid)
        self.assertTrue(any(issue.code == "INVALID_ENCODING_CHARACTERS" for issue in result.issues))
        
        # Test missing EVN
        missing_evn_file = self.source_dir / "missing_evn.hl7"
        result = self.validator.validate_file(str(missing_evn_file))
        self.assertFalse(result.is_valid)
        self.assertTrue(any(issue.code == "MISSING_EVN_SEGMENT" for issue in result.issues))
        
        # Test wrong version
        wrong_version_file = self.source_dir / "wrong_version.hl7"
        result = self.validator.validate_file(str(wrong_version_file))
        self.assertFalse(result.is_valid)
        self.assertTrue(any(issue.code == "INVALID_HL7_VERSION" for issue in result.issues))
    
    def test_enhancement_optimizer_fixes(self):
        """Test enhancement optimizer automatic fixes"""
        
        # Test encoding character fix
        with open(self.source_dir / "encoding_issue.hl7", 'r') as f:
            content = f.read()
        
        result = self.optimizer.enhance_message(content)
        self.assertTrue(result.success)
        self.assertTrue(any(action.action_type == "FIX_ENCODING" for action in result.actions_performed))
        
        # Validate the enhanced message
        validation_result = self.validator.validate_message(result.enhanced_content)
        encoding_issues = [issue for issue in validation_result.issues 
                          if issue.code == "INVALID_ENCODING_CHARACTERS"]
        self.assertEqual(len(encoding_issues), 0)
        
        # Test EVN segment addition
        with open(self.source_dir / "missing_evn.hl7", 'r') as f:
            content = f.read()
        
        result = self.optimizer.enhance_message(content)
        self.assertTrue(result.success)
        self.assertTrue(any(action.action_type == "ADD_SEGMENT" for action in result.actions_performed))
        
        # Validate the enhanced message
        validation_result = self.validator.validate_message(result.enhanced_content)
        evn_issues = [issue for issue in validation_result.issues 
                     if issue.code == "MISSING_EVN_SEGMENT"]
        self.assertEqual(len(evn_issues), 0)
    
    def test_quarantine_manager_categorization(self):
        """Test quarantine manager categorization and tracking"""
        
        # Test empty file quarantine
        empty_file = self.source_dir / "empty_file.hl7"
        validation_result = self.validator.validate_file(str(empty_file))
        
        quarantine_record = self.quarantine_manager.quarantine_message(
            str(empty_file), validation_result, 0
        )
        
        self.assertEqual(quarantine_record.category, QuarantineCategory.PARSING_ERROR)
        self.assertFalse(quarantine_record.manual_review_required)
        
        # Test malformed message quarantine
        malformed_file = self.source_dir / "malformed.hl7"
        validation_result = self.validator.validate_file(str(malformed_file))
        
        quarantine_record = self.quarantine_manager.quarantine_message(
            str(malformed_file), validation_result, 0
        )
        
        self.assertEqual(quarantine_record.category, QuarantineCategory.PARSING_ERROR)
        
        # Get quarantine statistics
        stats = self.quarantine_manager.get_quarantine_stats()
        self.assertEqual(stats.total_quarantined, 2)
        self.assertGreater(stats.by_category["PARSING_ERROR"], 0)
    
    def test_pipeline_orchestrator_complete_flow(self):
        """Test complete pipeline orchestration"""
        
        config = PipelineConfig(
            source_directory=str(self.source_dir),
            output_directory=str(self.output_dir),
            quarantine_directory=str(self.quarantine_dir),
            batch_size=10,
            max_enhancement_retries=2,
            log_level="DEBUG"
        )
        
        orchestrator = PipelineOrchestrator(config)
        result = orchestrator.run_pipeline()
        
        # Verify pipeline execution
        self.assertIn('execution_summary', result)
        self.assertGreater(result['execution_summary']['total_files_processed'], 0)
        
        # Check that valid files were processed
        self.assertGreater(result['execution_summary']['files_successfully_enhanced'] + 
                          result['execution_summary']['files_already_valid'], 0)
        
        # Check that problematic files were quarantined
        self.assertGreater(result['execution_summary']['files_quarantined'], 0)
        
        # Verify output files exist
        output_files = list(self.output_dir.glob("**/*.hl7"))
        self.assertGreater(len(output_files), 0)
        
        # Verify quarantine files exist
        quarantine_files = list(self.quarantine_dir.glob("**/*.hl7"))
        self.assertGreater(len(quarantine_files), 0)
    
    def test_pipeline_logger_functionality(self):
        """Test pipeline logger functionality"""
        
        # Test event logging
        self.logger.log_pipeline_start({'test': 'config'})
        self.logger.log_file_processing_start("test.hl7", 1024)
        self.logger.log_validation_result("test.hl7", True, 0, 50.0)
        self.logger.log_enhancement_result("test.hl7", True, 2, 100.0)
        self.logger.log_file_processing_end("test.hl7", True, 2)
        self.logger.log_pipeline_end({'files_processed': 1})
        
        # Get event summary
        summary = self.logger.get_event_summary()
        self.assertGreater(summary['total_events'], 0)
        self.assertIn('PIPELINE_START', summary['event_counts'])
        self.assertIn('PIPELINE_END', summary['event_counts'])
        
        # Test performance metrics
        self.logger.update_performance_metrics(files_per_second=10.5)
        performance_report = self.logger.generate_performance_report()
        self.assertIn('performance_summary', performance_report)
    
    def test_pipeline_performance_benchmarks(self):
        """Test pipeline performance benchmarks"""
        
        # Create larger test dataset
        large_test_dir = self.test_dir / "large_test"
        large_test_dir.mkdir(exist_ok=True)
        
        # Create 50 test files
        base_message = """MSH|^~\\&|TestApp|TestFacility|ReceivingApp|ReceivingFacility|20240902082341||ADT^A04^ADT_A04|MSG{:03d}|P|2.8
EVN|A04|20240902082341||||
PID|1|{:05d}^^^MRN^MRN||DOE^TEST{:03d}^MIDDLE||19800101|M|||123 MAIN ST^^ANYTOWN^ST^12345^USA||555-1234|||M"""
        
        for i in range(50):
            message = base_message.format(i, i, i)
            with open(large_test_dir / f"test_message_{i:03d}.hl7", 'w') as f:
                f.write(message)
        
        # Configure pipeline for performance test
        config = PipelineConfig(
            source_directory=str(large_test_dir),
            output_directory=str(self.output_dir / "performance_test"),
            quarantine_directory=str(self.quarantine_dir / "performance_test"),
            batch_size=25,
            max_enhancement_retries=1
        )
        
        # Run pipeline and measure performance
        start_time = time.time()
        orchestrator = PipelineOrchestrator(config)
        result = orchestrator.run_pipeline()
        end_time = time.time()
        
        # Performance assertions
        total_time = end_time - start_time
        files_per_second = result['execution_summary']['total_files_processed'] / total_time
        
        self.assertGreater(files_per_second, 1.0)  # Should process at least 1 file per second
        self.assertEqual(result['execution_summary']['total_files_processed'], 50)
        self.assertGreater(result['performance_metrics']['success_rate_percentage'], 90)
    
    def test_edge_cases_and_error_handling(self):
        """Test edge cases and error handling"""
        
        # Test with non-existent source directory
        config = PipelineConfig(
            source_directory="/non/existent/path",
            output_directory=str(self.output_dir),
            quarantine_directory=str(self.quarantine_dir)
        )
        
        orchestrator = PipelineOrchestrator(config)
        result = orchestrator.run_pipeline()
        
        # Should handle gracefully
        self.assertEqual(result['execution_summary']['total_files_processed'], 0)
        
        # Test with permission issues (mock)
        with patch('builtins.open', side_effect=PermissionError("Access denied")):
            try:
                self.validator.validate_file(str(self.source_dir / "valid_message.hl7"))
            except:
                pass  # Expected to fail gracefully
        
        # Test with corrupted HL7 content
        corrupted_content = "MSH|^~\\&|" + "x" * 10000  # Very long invalid content
        result = self.validator.validate_message(corrupted_content)
        self.assertFalse(result.is_valid)
    
    def test_retry_mechanism(self):
        """Test quarantine retry mechanism"""
        
        # Create a message that will be quarantined
        problematic_file = self.source_dir / "retry_test.hl7"
        with open(problematic_file, 'w') as f:
            f.write("Invalid HL7 content")
        
        validation_result = self.validator.validate_file(str(problematic_file))
        
        # Quarantine the message
        record = self.quarantine_manager.quarantine_message(
            str(problematic_file), validation_result, 1
        )
        
        # Test retry eligibility
        eligible_messages = self.quarantine_manager.get_retry_eligible_messages()
        
        # Update retry attempt
        self.quarantine_manager.update_retry_attempt(record, success=False)
        
        # Verify retry count increased
        self.assertEqual(record.retry_count, 1)
    
    def test_comprehensive_reporting(self):
        """Test comprehensive reporting functionality"""
        
        # Run a small pipeline
        config = PipelineConfig(
            source_directory=str(self.source_dir),
            output_directory=str(self.output_dir),
            quarantine_directory=str(self.quarantine_dir),
            generate_reports=True
        )
        
        orchestrator = PipelineOrchestrator(config)
        result = orchestrator.run_pipeline()
        
        # Check that reports were generated
        reports_dir = self.output_dir / "reports"
        self.assertTrue(reports_dir.exists())
        
        # Check for specific report files
        json_reports = list(reports_dir.glob("*.json"))
        csv_reports = list(reports_dir.glob("*.csv"))
        
        self.assertGreater(len(json_reports), 0)
        self.assertGreater(len(csv_reports), 0)
        
        # Validate report content
        for json_report in json_reports:
            with open(json_report, 'r') as f:
                report_data = json.load(f)
            self.assertIn('pipeline_execution', report_data)
    
    def test_memory_usage_and_cleanup(self):
        """Test memory usage and cleanup"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Process files multiple times to test memory management
        config = PipelineConfig(
            source_directory=str(self.source_dir),
            output_directory=str(self.output_dir / "memory_test"),
            quarantine_directory=str(self.quarantine_dir / "memory_test"),
            batch_size=5
        )
        
        for i in range(3):  # Run pipeline 3 times
            orchestrator = PipelineOrchestrator(config)
            result = orchestrator.run_pipeline()
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for this test)
        self.assertLess(memory_increase, 100)

if __name__ == '__main__':
    unittest.main()
