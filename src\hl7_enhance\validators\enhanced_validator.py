"""
Enhanced HL7 Validator for comprehensive v2.8 compliance validation
"""

import hl7
import logging
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path


class ValidationSeverity(Enum):
    """Validation error severity levels"""
    ERROR = "ERROR"
    WARNING = "WARNING"
    INFO = "INFO"


@dataclass
class ValidationIssue:
    """Represents a validation issue found in an HL7 message"""
    severity: ValidationSeverity
    code: str
    message: str
    segment: Optional[str] = None
    field: Optional[str] = None
    location: Optional[str] = None
    fix_suggestion: Optional[str] = None


@dataclass
class ValidationResult:
    """Comprehensive validation result for an HL7 message"""
    is_valid: bool
    message_type: str
    version: str
    issues: List[ValidationIssue]
    statistics: Dict[str, Any]
    
    def get_errors(self) -> List[ValidationIssue]:
        """Get only error-level issues"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.ERROR]
    
    def get_warnings(self) -> List[ValidationIssue]:
        """Get only warning-level issues"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.WARNING]
    
    def has_errors(self) -> bool:
        """Check if there are any error-level issues"""
        return len(self.get_errors()) > 0


class EnhancedHL7Validator:
    """Enhanced HL7 validator with comprehensive v2.8 compliance checking"""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the enhanced validator"""
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config(config_path)
        
        # HL7 v2.8 validation rules
        self.target_version = "2.8"
        self.encoding_chars = "^~\\&"
        
        # Message type requirements
        self.message_requirements = {
            'ADT': {
                'required_segments': ['MSH', 'EVN', 'PID'],
                'optional_segments': ['PV1', 'OBX', 'AL1', 'DG1'],
                'segment_order': ['MSH', 'EVN', 'PID', 'PV1', 'OBX', 'AL1', 'DG1']
            },
            'SIU': {
                'required_segments': ['MSH', 'SCH', 'PID'],
                'optional_segments': ['PV1', 'RGS', 'AIG', 'AIL', 'AIP', 'AIS'],
                'segment_order': ['MSH', 'SCH', 'PID', 'PV1', 'RGS', 'AIG', 'AIL', 'AIP', 'AIS']
            },
            'ORM': {
                'required_segments': ['MSH', 'PID', 'ORC'],
                'optional_segments': ['PV1', 'OBR', 'OBX'],
                'segment_order': ['MSH', 'PID', 'PV1', 'ORC', 'OBR', 'OBX']
            }
        }
        
        # MSH field requirements for v2.8
        self.msh_field_requirements = {
            1: {'name': 'Field Separator', 'required': True, 'value': '|'},
            2: {'name': 'Encoding Characters', 'required': True, 'value': '^~\\&'},
            3: {'name': 'Sending Application', 'required': True, 'min_length': 1},
            4: {'name': 'Sending Facility', 'required': False},
            5: {'name': 'Receiving Application', 'required': False},
            6: {'name': 'Receiving Facility', 'required': False},
            7: {'name': 'Date/Time of Message', 'required': True, 'min_length': 8},
            8: {'name': 'Security', 'required': False},
            9: {'name': 'Message Type', 'required': True, 'min_length': 3},
            10: {'name': 'Message Control ID', 'required': True, 'min_length': 1},
            11: {'name': 'Processing ID', 'required': True, 'valid_values': ['P', 'T', 'D']},
            12: {'name': 'Version ID', 'required': True, 'value': '2.8'}
        }
    
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """Load validation configuration"""
        if config_path and Path(config_path).exists():
            try:
                with open(config_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"Failed to load config from {config_path}: {e}")
        
        # Return default configuration
        return {
            'strict_mode': True,
            'validate_encoding': True,
            'validate_segments': True,
            'validate_fields': True,
            'validate_ordering': True
        }
    
    def validate_message(self, message_content: str, file_path: Optional[str] = None) -> ValidationResult:
        """Perform comprehensive validation of an HL7 message"""
        issues = []
        statistics = {
            'total_segments': 0,
            'validation_time': 0,
            'file_path': file_path
        }
        
        try:
            # Parse the message
            if '\n' in message_content and '\r' not in message_content:
                message_content = message_content.replace('\n', '\r')
            
            parsed_message = hl7.parse(message_content)
            statistics['total_segments'] = len(parsed_message)
            
            # Extract basic message info
            msh = parsed_message.segment('MSH')
            message_type = self._extract_message_type(msh)
            version = self._extract_version(msh)
            
            # Perform validation checks
            issues.extend(self._validate_msh_segment(msh))
            issues.extend(self._validate_encoding_characters(msh))
            issues.extend(self._validate_version_compliance(msh))
            issues.extend(self._validate_message_structure(parsed_message, message_type))
            issues.extend(self._validate_segment_ordering(parsed_message, message_type))
            issues.extend(self._validate_required_fields(parsed_message))
            
            # Determine if message is valid (no errors)
            is_valid = not any(issue.severity == ValidationSeverity.ERROR for issue in issues)
            
            return ValidationResult(
                is_valid=is_valid,
                message_type=message_type,
                version=version,
                issues=issues,
                statistics=statistics
            )
            
        except Exception as e:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                code="PARSING_ERROR",
                message=f"Failed to parse HL7 message: {str(e)}",
                fix_suggestion="Check message format and encoding"
            ))
            
            return ValidationResult(
                is_valid=False,
                message_type="UNKNOWN",
                version="UNKNOWN",
                issues=issues,
                statistics=statistics
            )

    def _extract_message_type(self, msh) -> str:
        """Extract message type from MSH segment"""
        try:
            if len(msh) > 9:
                message_type_field = str(msh[9])
                return message_type_field.split('^')[0] if '^' in message_type_field else message_type_field
        except:
            pass
        return "UNKNOWN"

    def _extract_version(self, msh) -> str:
        """Extract HL7 version from MSH segment"""
        try:
            if len(msh) > 12:
                return str(msh[12]).strip()
        except:
            pass
        return "UNKNOWN"

    def _validate_msh_segment(self, msh) -> List[ValidationIssue]:
        """Validate MSH segment fields"""
        issues = []

        for field_num, requirements in self.msh_field_requirements.items():
            if field_num >= len(msh):
                if requirements.get('required', False):
                    issues.append(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        code="MISSING_REQUIRED_FIELD",
                        message=f"Required field MSH.{field_num} ({requirements['name']}) is missing",
                        segment="MSH",
                        field=str(field_num),
                        fix_suggestion=f"Add {requirements['name']} field to MSH segment"
                    ))
                continue

            field_value = str(msh[field_num]).strip()

            # Check required fields
            if requirements.get('required', False) and not field_value:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    code="EMPTY_REQUIRED_FIELD",
                    message=f"Required field MSH.{field_num} ({requirements['name']}) is empty",
                    segment="MSH",
                    field=str(field_num),
                    fix_suggestion=f"Provide value for {requirements['name']}"
                ))
                continue

            # Check specific values
            if 'value' in requirements and field_value and field_value != requirements['value']:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    code="INVALID_FIELD_VALUE",
                    message=f"MSH.{field_num} has invalid value '{field_value}', expected '{requirements['value']}'",
                    segment="MSH",
                    field=str(field_num),
                    fix_suggestion=f"Change value to '{requirements['value']}'"
                ))

            # Check valid values
            if 'valid_values' in requirements and field_value and field_value not in requirements['valid_values']:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    code="INVALID_FIELD_VALUE",
                    message=f"MSH.{field_num} has invalid value '{field_value}', expected one of {requirements['valid_values']}",
                    segment="MSH",
                    field=str(field_num),
                    fix_suggestion=f"Use one of: {', '.join(requirements['valid_values'])}"
                ))

            # Check minimum length
            if 'min_length' in requirements and field_value and len(field_value) < requirements['min_length']:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.WARNING,
                    code="FIELD_TOO_SHORT",
                    message=f"MSH.{field_num} is too short: {len(field_value)} chars, minimum {requirements['min_length']}",
                    segment="MSH",
                    field=str(field_num),
                    fix_suggestion=f"Ensure field has at least {requirements['min_length']} characters"
                ))

        return issues

    def _validate_encoding_characters(self, msh) -> List[ValidationIssue]:
        """Validate encoding characters in MSH.2"""
        issues = []

        if len(msh) > 2:
            encoding_chars = str(msh[2])
            if encoding_chars != self.encoding_chars:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    code="INVALID_ENCODING_CHARACTERS",
                    message=f"Invalid encoding characters: '{encoding_chars}', expected '{self.encoding_chars}'",
                    segment="MSH",
                    field="2",
                    fix_suggestion=f"Change encoding characters to '{self.encoding_chars}'"
                ))

        return issues

    def _validate_version_compliance(self, msh) -> List[ValidationIssue]:
        """Validate HL7 version compliance"""
        issues = []

        if len(msh) > 12:
            version = str(msh[12]).strip()
            if version != self.target_version:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    code="INVALID_HL7_VERSION",
                    message=f"Invalid HL7 version: '{version}', expected '{self.target_version}'",
                    segment="MSH",
                    field="12",
                    fix_suggestion=f"Update version to '{self.target_version}'"
                ))

        return issues

    def _validate_message_structure(self, message, message_type: str) -> List[ValidationIssue]:
        """Validate message structure and required segments"""
        issues = []

        if message_type not in self.message_requirements:
            issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                code="UNKNOWN_MESSAGE_TYPE",
                message=f"Unknown message type: {message_type}",
                fix_suggestion="Verify message type is correct"
            ))
            return issues

        requirements = self.message_requirements[message_type]
        present_segments = [str(seg[0]) for seg in message]

        # Check required segments
        for required_segment in requirements['required_segments']:
            if required_segment not in present_segments:
                issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    code="MISSING_REQUIRED_SEGMENT",
                    message=f"Missing required segment: {required_segment}",
                    segment=required_segment,
                    fix_suggestion=f"Add {required_segment} segment to message"
                ))

        return issues

    def _validate_segment_ordering(self, message, message_type: str) -> List[ValidationIssue]:
        """Validate segment ordering according to HL7 standards"""
        issues = []

        if message_type not in self.message_requirements:
            return issues

        requirements = self.message_requirements[message_type]
        expected_order = requirements['segment_order']
        present_segments = [str(seg[0]) for seg in message]

        # Check if segments are in correct order
        last_expected_index = -1
        for segment in present_segments:
            if segment in expected_order:
                current_index = expected_order.index(segment)
                if current_index < last_expected_index:
                    issues.append(ValidationIssue(
                        severity=ValidationSeverity.WARNING,
                        code="INCORRECT_SEGMENT_ORDER",
                        message=f"Segment {segment} appears out of order",
                        segment=segment,
                        fix_suggestion=f"Move {segment} to correct position according to HL7 standards"
                    ))
                last_expected_index = max(last_expected_index, current_index)

        return issues

    def _validate_required_fields(self, message) -> List[ValidationIssue]:
        """Validate required fields in segments"""
        issues = []

        # Validate EVN segment for ADT messages
        try:
            msh = message.segment('MSH')
            if len(msh) > 9:
                message_type = str(msh[9]).split('^')[0]
                if message_type == 'ADT':
                    try:
                        evn = message.segment('EVN')
                        if not evn:
                            issues.append(ValidationIssue(
                                severity=ValidationSeverity.ERROR,
                                code="MISSING_EVN_SEGMENT",
                                message="EVN segment is required for ADT messages",
                                segment="EVN",
                                fix_suggestion="Add EVN segment after MSH segment"
                            ))
                    except:
                        issues.append(ValidationIssue(
                            severity=ValidationSeverity.ERROR,
                            code="MISSING_EVN_SEGMENT",
                            message="EVN segment is required for ADT messages",
                            segment="EVN",
                            fix_suggestion="Add EVN segment after MSH segment"
                        ))
        except:
            pass

        return issues

    def validate_file(self, file_path: str) -> ValidationResult:
        """Validate an HL7 file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            if not content.strip():
                return ValidationResult(
                    is_valid=False,
                    message_type="UNKNOWN",
                    version="UNKNOWN",
                    issues=[ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        code="EMPTY_FILE",
                        message="File is empty",
                        fix_suggestion="Provide valid HL7 message content"
                    )],
                    statistics={'file_path': file_path}
                )

            return self.validate_message(content, file_path)

        except Exception as e:
            return ValidationResult(
                is_valid=False,
                message_type="UNKNOWN",
                version="UNKNOWN",
                issues=[ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    code="FILE_READ_ERROR",
                    message=f"Failed to read file: {str(e)}",
                    fix_suggestion="Check file permissions and encoding"
                )],
                statistics={'file_path': file_path}
            )

    def validate_directory(self, directory_path: str) -> List[ValidationResult]:
        """Validate all HL7 files in a directory"""
        results = []
        directory = Path(directory_path)

        if not directory.exists():
            self.logger.error(f"Directory does not exist: {directory_path}")
            return results

        # Find all HL7 files
        hl7_files = list(directory.rglob('*.hl7'))

        for file_path in hl7_files:
            result = self.validate_file(str(file_path))
            results.append(result)

        return results

    def generate_report(self, results: List[ValidationResult]) -> Dict:
        """Generate comprehensive validation report"""
        total_messages = len(results)
        valid_messages = sum(1 for r in results if r.is_valid)
        invalid_messages = total_messages - valid_messages

        # Collect issue statistics
        issue_counts = {}
        severity_counts = {severity.value: 0 for severity in ValidationSeverity}

        for result in results:
            for issue in result.issues:
                issue_counts[issue.code] = issue_counts.get(issue.code, 0) + 1
                severity_counts[issue.severity.value] += 1

        # Message type statistics
        message_type_counts = {}
        for result in results:
            message_type_counts[result.message_type] = message_type_counts.get(result.message_type, 0) + 1

        return {
            'summary': {
                'total_messages': total_messages,
                'valid_messages': valid_messages,
                'invalid_messages': invalid_messages,
                'success_rate': (valid_messages / total_messages * 100) if total_messages > 0 else 0
            },
            'issue_statistics': {
                'by_code': issue_counts,
                'by_severity': severity_counts
            },
            'message_types': message_type_counts,
            'detailed_results': [
                {
                    'file_path': result.statistics.get('file_path'),
                    'is_valid': result.is_valid,
                    'message_type': result.message_type,
                    'version': result.version,
                    'error_count': len(result.get_errors()),
                    'warning_count': len(result.get_warnings()),
                    'issues': [
                        {
                            'severity': issue.severity.value,
                            'code': issue.code,
                            'message': issue.message,
                            'segment': issue.segment,
                            'field': issue.field,
                            'fix_suggestion': issue.fix_suggestion
                        } for issue in result.issues
                    ]
                } for result in results
            ]
        }
