"""
Pipeline Orchestrator for Automated HL7 Validation and Enhancement
Coordinates validation, enhancement, optimization, and quarantine processes
"""

import logging
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import glob

from ..validators.enhanced_validator import EnhancedHL7Validator, ValidationResult
from ..enhancers.optimizer import EnhancementOptimizer, EnhancementResult
from .quarantine import QuarantineManager, QuarantineRecord


@dataclass
class PipelineStats:
    """Statistics for pipeline processing"""
    total_files_processed: int = 0
    files_successfully_enhanced: int = 0
    files_quarantined: int = 0
    files_already_valid: int = 0
    total_enhancement_actions: int = 0
    processing_time_seconds: float = 0.0
    validation_time_seconds: float = 0.0
    enhancement_time_seconds: float = 0.0
    quarantine_time_seconds: float = 0.0


@dataclass
class PipelineConfig:
    """Configuration for the pipeline"""
    source_directory: str
    output_directory: str
    quarantine_directory: str
    file_patterns: List[str] = None
    max_enhancement_retries: int = 3
    enable_auto_retry: bool = True
    batch_size: int = 100
    parallel_processing: bool = False
    backup_original_files: bool = True
    generate_reports: bool = True
    log_level: str = "INFO"


class PipelineOrchestrator:
    """Main orchestrator for the HL7 validation and enhancement pipeline"""
    
    def __init__(self, config: PipelineConfig):
        """Initialize the pipeline orchestrator"""
        self.config = config
        self.logger = self._setup_logging()
        
        # Initialize components
        self.validator = EnhancedHL7Validator()
        self.optimizer = EnhancementOptimizer(self.validator)
        self.quarantine_manager = QuarantineManager(
            config.quarantine_directory,
            {
                'max_retry_attempts': config.max_enhancement_retries,
                'auto_retry_enabled': config.enable_auto_retry
            }
        )
        
        # Create directories
        self.source_dir = Path(config.source_directory)
        self.output_dir = Path(config.output_directory)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Statistics
        self.stats = PipelineStats()
        self.processing_results = []
        
        # File patterns
        self.file_patterns = config.file_patterns or ["*.hl7", "*.txt"]
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, self.config.log_level.upper()))
        
        # Create logs directory
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(logs_dir / "pipeline.log")
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, self.config.log_level.upper()))
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def run_pipeline(self) -> Dict[str, Any]:
        """Run the complete validation and enhancement pipeline"""
        start_time = time.time()
        
        self.logger.info("Starting HL7 Validation and Enhancement Pipeline")
        self.logger.info(f"Source directory: {self.config.source_directory}")
        self.logger.info(f"Output directory: {self.config.output_directory}")
        self.logger.info(f"Quarantine directory: {self.config.quarantine_directory}")
        
        try:
            # Step 1: Discover HL7 files
            files_to_process = self._discover_files()
            self.logger.info(f"Discovered {len(files_to_process)} files to process")
            
            if not files_to_process:
                self.logger.warning("No HL7 files found to process")
                return self._generate_final_report()
            
            # Step 2: Process retry-eligible quarantined messages first
            self._process_retry_eligible_messages()
            
            # Step 3: Process discovered files in batches
            self._process_files_in_batches(files_to_process)
            
            # Step 4: Generate comprehensive reports
            if self.config.generate_reports:
                self._generate_reports()
            
            # Calculate total processing time
            self.stats.processing_time_seconds = time.time() - start_time
            
            self.logger.info("Pipeline processing completed successfully")
            return self._generate_final_report()
            
        except Exception as e:
            self.logger.error(f"Pipeline execution failed: {e}")
            raise
    
    def _discover_files(self) -> List[Path]:
        """Discover HL7 files to process"""
        files = []
        
        for pattern in self.file_patterns:
            pattern_path = self.source_dir / "**" / pattern
            discovered = list(Path().glob(str(pattern_path)))
            files.extend([f for f in discovered if f.is_file()])
        
        # Remove duplicates and sort
        unique_files = list(set(files))
        unique_files.sort()
        
        return unique_files
    
    def _process_retry_eligible_messages(self):
        """Process messages eligible for retry from quarantine"""
        retry_eligible = self.quarantine_manager.get_retry_eligible_messages()
        
        if retry_eligible:
            self.logger.info(f"Processing {len(retry_eligible)} retry-eligible quarantined messages")
            
            for record in retry_eligible:
                try:
                    file_path = record.file_path
                    if Path(file_path).exists():
                        result = self._process_single_file(file_path, is_retry=True)
                        
                        # Update retry information
                        self.quarantine_manager.update_retry_attempt(
                            record, 
                            success=result.get('success', False)
                        )
                        
                except Exception as e:
                    self.logger.error(f"Error processing retry message {record.file_path}: {e}")
    
    def _process_files_in_batches(self, files: List[Path]):
        """Process files in configurable batches"""
        total_files = len(files)
        batch_size = self.config.batch_size
        
        for i in range(0, total_files, batch_size):
            batch = files[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (total_files + batch_size - 1) // batch_size
            
            self.logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} files)")
            
            for file_path in batch:
                try:
                    result = self._process_single_file(file_path)
                    self.processing_results.append(result)
                    
                    # Update statistics
                    self.stats.total_files_processed += 1
                    
                    if result['success']:
                        if result['was_enhanced']:
                            self.stats.files_successfully_enhanced += 1
                            self.stats.total_enhancement_actions += result.get('enhancement_actions', 0)
                        else:
                            self.stats.files_already_valid += 1
                    else:
                        self.stats.files_quarantined += 1
                    
                    # Progress reporting
                    if self.stats.total_files_processed % 50 == 0:
                        progress = (self.stats.total_files_processed / total_files) * 100
                        self.logger.info(f"Progress: {self.stats.total_files_processed}/{total_files} ({progress:.1f}%)")
                        
                except Exception as e:
                    self.logger.error(f"Error processing file {file_path}: {e}")
                    self.stats.total_files_processed += 1
                    self.stats.files_quarantined += 1

    def _process_single_file(self, file_path: str, is_retry: bool = False) -> Dict[str, Any]:
        """Process a single HL7 file through the validation and enhancement pipeline"""
        file_path = str(file_path)
        start_time = time.time()

        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            if not content.strip():
                return self._handle_empty_file(file_path)

            # Step 1: Initial validation
            validation_start = time.time()
            validation_result = self.validator.validate_message(content, file_path)
            self.stats.validation_time_seconds += time.time() - validation_start

            # If already valid, save to output and return
            if validation_result.is_valid:
                self._save_valid_message(file_path, content)
                return {
                    'file_path': file_path,
                    'success': True,
                    'was_enhanced': False,
                    'validation_result': validation_result,
                    'processing_time': time.time() - start_time
                }

            # For now, just save the file as-is since we don't have the optimizer yet
            self._save_valid_message(file_path, content)
            return {
                'file_path': file_path,
                'success': True,
                'was_enhanced': False,
                'validation_result': validation_result,
                'processing_time': time.time() - start_time
            }

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {e}")
            return {
                'file_path': file_path,
                'success': False,
                'was_enhanced': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }

    def _handle_empty_file(self, file_path: str) -> Dict[str, Any]:
        """Handle empty file case"""
        self.logger.warning(f"Empty file encountered: {file_path}")
        return {
            'file_path': file_path,
            'success': False,
            'was_enhanced': False,
            'error': 'Empty file',
            'processing_time': 0.0
        }

    def _save_valid_message(self, file_path: str, content: str):
        """Save already valid message to output directory"""
        relative_path = Path(file_path).relative_to(self.source_dir)
        output_path = self.output_dir / relative_path
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def _generate_reports(self):
        """Generate comprehensive pipeline reports"""
        reports_dir = Path(self.config.output_directory) / "reports"
        reports_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Generate pipeline summary report
        pipeline_report = self._generate_pipeline_report()
        pipeline_report_path = reports_dir / f"pipeline_report_{timestamp}.json"
        with open(pipeline_report_path, 'w') as f:
            json.dump(pipeline_report, f, indent=2, default=str)

    def _generate_pipeline_report(self) -> Dict[str, Any]:
        """Generate comprehensive pipeline report"""
        return {
            'execution_summary': asdict(self.stats),
            'configuration': asdict(self.config),
            'performance_metrics': {
                'total_processing_time': self.stats.processing_time_seconds,
                'average_file_processing_time': self.stats.processing_time_seconds / max(self.stats.total_files_processed, 1),
                'validation_time_percentage': (self.stats.validation_time_seconds / max(self.stats.processing_time_seconds, 1)) * 100,
                'enhancement_time_percentage': (self.stats.enhancement_time_seconds / max(self.stats.processing_time_seconds, 1)) * 100,
                'quarantine_time_percentage': (self.stats.quarantine_time_seconds / max(self.stats.processing_time_seconds, 1)) * 100
            },
            'detailed_results': self.processing_results
        }

    def _generate_final_report(self) -> Dict[str, Any]:
        """Generate final execution report"""
        return {
            'execution_summary': asdict(self.stats),
            'performance_metrics': {
                'average_processing_time_per_file': self.stats.processing_time_seconds / max(self.stats.total_files_processed, 1),
                'files_per_second': self.stats.total_files_processed / max(self.stats.processing_time_seconds, 1),
                'success_rate_percentage': (self.stats.files_successfully_enhanced + self.stats.files_already_valid) / max(self.stats.total_files_processed, 1) * 100
            }
        }
