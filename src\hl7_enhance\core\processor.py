"""
Core HL7 message processor
"""

import os
import sys
import json
import logging
import traceback
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
import time
import re
import shutil

try:
    import hl7
    import requests
    from dotenv import load_dotenv
except ImportError as e:
    print(f"❌ Missing required dependency: {e}")
    print("Please install dependencies: pip install -r requirements.txt")
    sys.exit(1)

from .exceptions import HL7ProcessingError

# Load environment variables
load_dotenv()


class HL7Processor:
    """Main HL7 processing class"""
    
    def __init__(self, source_dir: str, output_dir: str, verbose: bool = False):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.quarantine_dir = Path("data/quarantine")
        self.verbose = verbose
        
        # Statistics
        self.stats = {
            'files_processed': 0,
            'files_enhanced': 0,
            'errors_encountered': 0,
            'files_quarantined': 0,
            'message_types': {}  # Track different message types processed
        }
        
        # Setup logging
        self.setup_logging()
        
        # Load configuration
        self.load_config()
        
        # Create directories
        self.create_directories()
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_level = logging.DEBUG if self.verbose else logging.INFO
        
        # Create logs directory if it doesn't exist
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        # Setup activity log
        self.activity_log_path = logs_dir / "processing_activity.log"
        self.error_log_path = logs_dir / "error_details.log"
        
        # Configure logger
        self.logger = logging.getLogger('HL7Processor')
        self.logger.setLevel(log_level)
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # File handler for activity log
        file_handler = logging.FileHandler(self.activity_log_path, encoding='utf-8')
        file_handler.setLevel(log_level)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def log_error(self, error: HL7ProcessingError, context: Dict[str, Any] = None):
        """Log error in machine-readable JSON format"""
        error_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': 'error',
            'message': error.message,
            'filePath': str(error.file_path) if error.file_path else None,
            'errorCode': error.error_code,
            'errorContext': context or {},
            'service': 'hl7-enhancer'
        }
        
        with open(self.error_log_path, 'a', encoding='utf-8') as f:
            f.write(json.dumps(error_entry) + '\n')
    
    def load_config(self):
        """Load configuration from mapping_rules.json"""
        config_path = Path("config/mapping_rules.json")
        
        # Try legacy location if new location doesn't exist
        if not config_path.exists():
            config_path = Path("mapping_rules.json")
        
        try:
            if config_path.exists():
                with open(config_path, 'r') as f:
                    self.mapping_rules = json.load(f)
                self.logger.info(f"Loaded {len(self.mapping_rules)} mapping rules from {config_path}")
            else:
                self.mapping_rules = []
                self.logger.warning("No mapping rules file found, using empty rules")
        except Exception as e:
            self.logger.error(f"Failed to load mapping rules: {e}")
            self.mapping_rules = []
    
    def create_directories(self):
        """Create necessary directories"""
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            self.quarantine_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created directories: {self.output_dir}, {self.quarantine_dir}")
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to create directories: {e}",
                "DIRECTORY_ERROR"
            )
    
    def find_hl7_files(self) -> List[Path]:
        """Recursively find all HL7 files"""
        hl7_files = []
        try:
            for file_path in self.source_dir.rglob('*.hl7'):
                if file_path.is_file():
                    hl7_files.append(file_path)
            return hl7_files
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to scan directory {self.source_dir}: {e}",
                "DIRECTORY_ERROR"
            )

    def parse_hl7_message(self, file_path: Path) -> hl7.Message:
        """Parse HL7 file into message object with enhanced support for different message types"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()

            if not content:
                raise HL7ProcessingError(
                    "Empty file",
                    "HL7_PARSING_FAILURE",
                    str(file_path)
                )

            # Preprocess HL7 content: ensure proper segment separators
            # HL7 library expects \r as segment separator, not \n

            # Store original line ending for output formatting
            self.original_line_ending = '\n' if '\n' in content else '\r'

            if '\n' in content and '\r' not in content:
                # Convert newlines to carriage returns for HL7 library
                content = content.replace('\n', '\r')
            elif '\n' not in content and '\r' not in content:
                # Split by segment identifiers and rejoin with carriage returns
                # Enhanced pattern to include SIU message segments
                segment_pattern = r'(?=MSH|EVN|PID|PV1|OBX|OBR|NTE|AL1|DG1|PR1|GT1|IN1|IN2|IN3|ACC|UB1|UB2|ZQA|ZFM|SCH|RGS|AIL|AIG|AIL|AIP|AIS|ZZZ)'
                content = re.sub(segment_pattern, r'\r', content)
                content = content.strip()

            # Parse HL7 message (hl7 library expects \r as segment separator)
            message = hl7.parse(content)

            # Validate MSH segment exists
            if not message.segment('MSH'):
                raise HL7ProcessingError(
                    "MSH segment not found",
                    "HL7_PARSING_FAILURE",
                    str(file_path)
                )

            # Detect and store message type for type-specific processing
            msh = message.segment('MSH')
            if len(msh) > 9:
                message_type_field = str(msh[9]).strip()
                message_type = message_type_field.split('^')[0] if '^' in message_type_field else message_type_field
                self.current_message_type = message_type
                self.logger.debug(f"Detected message type: {message_type}")
            else:
                self.current_message_type = "UNKNOWN"
                self.logger.warning(f"Could not determine message type for {file_path}")

            return message

        except hl7.ParseException as e:
            raise HL7ProcessingError(
                f"HL7 parsing failed: {e}",
                "HL7_PARSING_FAILURE",
                str(file_path)
            )
        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to read file: {e}",
                "FILE_READ_ERROR",
                str(file_path)
            )

    def apply_hl7_enhancements(self, message: hl7.Message) -> hl7.Message:
        """Apply standard HL7 enhancements with message type-specific logic"""
        try:
            # Get MSH segment
            msh = message.segment('MSH')

            # Update version to 2.8
            msh[12] = '2.8'

            # Set processing ID to Production
            msh[11] = 'P'

            # Apply message type-specific enhancements
            if hasattr(self, 'current_message_type'):
                if self.current_message_type == 'ADT':
                    self.ensure_evn_segment(message)
                elif self.current_message_type == 'SIU':
                    self.enhance_siu_message(message)

            # Apply OBX mapping rules
            self.apply_obx_mapping(message)

            return message

        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to apply enhancements: {e}",
                "PROCESSING_ERROR"
            )

    def ensure_evn_segment(self, message: hl7.Message):
        """Ensure ADT messages have required EVN segment for HL7 v2.8 compliance"""
        try:
            # Check if EVN segment already exists
            try:
                evn_segment = message.segment('EVN')
                self.logger.debug("EVN segment already exists")
                return
            except KeyError:
                # EVN segment doesn't exist, create it
                pass

            # Get MSH segment for timestamp and message type
            msh = message.segment('MSH')
            timestamp = str(msh[7]) if len(msh) > 7 else datetime.now().strftime("%Y%m%d%H%M%S")

            # Extract event type from MSH-9 (e.g., ADT^A04 -> A04)
            message_type_field = str(msh[9]) if len(msh) > 9 else ""
            event_type = "A04"  # Default
            if '^' in message_type_field:
                parts = message_type_field.split('^')
                if len(parts) > 1:
                    event_type = parts[1]

            # Create EVN segment: EVN|event_type||timestamp
            evn_segment = hl7.Segment('EVN', [event_type, '', timestamp])

            # Insert EVN segment after MSH (position 1)
            message.insert(1, evn_segment)

            self.logger.debug(f"Added EVN segment for ADT message: {event_type}")

        except Exception as e:
            self.logger.warning(f"Failed to ensure EVN segment: {e}")

    def enhance_siu_message(self, message: hl7.Message):
        """Apply SIU-specific enhancements for HL7 v2.8 compliance"""
        try:
            # SIU messages are generally well-formed, but ensure proper structure
            # Check for required segments: MSH, SCH, PID
            required_segments = ['MSH', 'SCH', 'PID']
            missing_segments = []

            for seg_name in required_segments:
                try:
                    message.segment(seg_name)
                except KeyError:
                    missing_segments.append(seg_name)

            if missing_segments:
                self.logger.warning(f"SIU message missing required segments: {missing_segments}")
            else:
                self.logger.debug("SIU message has all required segments")

        except Exception as e:
            self.logger.warning(f"Failed to enhance SIU message: {e}")

    def apply_obx_mapping(self, message: hl7.Message):
        """Apply OBX segment mapping rules"""
        try:
            obx_segments = []
            segments_to_remove = []

            # Collect all OBX segments
            for i, segment in enumerate(message):
                segment_name = str(segment[0][0]) if len(segment) > 0 and len(segment[0]) > 0 else ''
                if segment_name == 'OBX':
                    obx_segments.append((i, segment))

            # Apply mapping rules
            for rule in self.mapping_rules:
                for seg_index, obx_segment in obx_segments:
                    # Check if this OBX matches the rule
                    # Extract OBX-3.1 (first component of observation identifier)
                    obx_field_value = ''
                    if len(obx_segment) > 3:
                        obx3_components = str(obx_segment[3]).split('^')
                        obx_field_value = obx3_components[0] if len(obx3_components) > 0 else ''

                    if obx_field_value == rule['obxValue']:
                        self.logger.debug(f"Applying mapping rule: {rule['description']}")

                        # Extract the value from OBX-5 (observation value)
                        obx_value = str(obx_segment[5]) if len(obx_segment) > 5 else ''

                        # Apply the mapping based on target segment
                        if rule['targetSegment'] == 'PID':
                            self.map_to_pid_segment(message, rule['targetField'], obx_value)
                        elif rule['targetSegment'] == 'ROL':
                            self.map_to_rol_segment(message, rule['targetField'], obx_value)
                        elif rule['targetSegment'] == 'PD1':
                            self.map_to_pd1_segment(message, rule['targetField'], obx_value)

                        # Mark for removal if specified
                        if rule.get('removeOriginal', False):
                            segments_to_remove.append(seg_index)

            # Remove segments marked for removal (in reverse order to maintain indices)
            for seg_index in sorted(segments_to_remove, reverse=True):
                del message[seg_index]
                self.logger.debug(f"Removed OBX segment at index {seg_index}")

        except Exception as e:
            self.logger.warning(f"Failed to apply OBX mapping: {e}")

    def map_to_pid_segment(self, message: hl7.Message, target_field: str, value: str):
        """Map value to PID segment field"""
        try:
            pid_segment = message.segment('PID')
            field_parts = target_field.split('.')

            if len(field_parts) == 2:
                field_num = int(field_parts[0])
                component_num = int(field_parts[1])

                # Ensure the field exists and has enough components
                while len(pid_segment) <= field_num:
                    pid_segment.append('')

                # Handle component assignment
                field_value = str(pid_segment[field_num])
                components = field_value.split('^') if field_value else ['']

                # Ensure enough components
                while len(components) <= component_num:
                    components.append('')

                components[component_num] = value
                pid_segment[field_num] = '^'.join(components)

                self.logger.debug(f"Mapped value '{value}' to PID-{target_field}")

        except Exception as e:
            self.logger.warning(f"Failed to map to PID segment: {e}")

    def map_to_rol_segment(self, message: hl7.Message, target_field: str, value: str):
        """Map value to ROL segment field"""
        try:
            # Check if ROL segment exists, create if not
            try:
                rol_segment = message.segment('ROL')
            except KeyError:
                # Create new ROL segment
                rol_segment = hl7.Segment('ROL', ['', '', '', ''])
                # Insert after PID segment
                pid_index = None
                for i, segment in enumerate(message):
                    if str(segment[0][0]) == 'PID':
                        pid_index = i
                        break

                if pid_index is not None:
                    message.insert(pid_index + 1, rol_segment)
                else:
                    message.append(rol_segment)

            field_parts = target_field.split('.')
            field_num = int(field_parts[0])

            # Ensure the field exists
            while len(rol_segment) <= field_num:
                rol_segment.append('')

            rol_segment[field_num] = value
            self.logger.debug(f"Mapped value '{value}' to ROL-{target_field}")

        except Exception as e:
            self.logger.warning(f"Failed to map to ROL segment: {e}")

    def map_to_pd1_segment(self, message: hl7.Message, target_field: str, value: str):
        """Map value to PD1 segment field"""
        try:
            # Check if PD1 segment exists, create if not
            try:
                pd1_segment = message.segment('PD1')
            except KeyError:
                # Create new PD1 segment
                pd1_segment = hl7.Segment('PD1', ['', '', '', ''])
                # Insert after PID segment
                pid_index = None
                for i, segment in enumerate(message):
                    if str(segment[0][0]) == 'PID':
                        pid_index = i
                        break

                if pid_index is not None:
                    message.insert(pid_index + 1, pd1_segment)
                else:
                    message.append(pd1_segment)

            field_parts = target_field.split('.')
            field_num = int(field_parts[0])

            # Ensure the field exists
            while len(pd1_segment) <= field_num:
                pd1_segment.append('')

            pd1_segment[field_num] = value
            self.logger.debug(f"Mapped value '{value}' to PD1-{target_field}")

        except Exception as e:
            self.logger.warning(f"Failed to map to PD1 segment: {e}")

    def save_enhanced_message(self, message: hl7.Message, original_path: Path):
        """Save enhanced message preserving directory structure with proper formatting"""
        try:
            # Calculate relative path from source directory
            relative_path = original_path.relative_to(self.source_dir)
            output_path = self.output_dir / relative_path

            # Create parent directories
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Convert message back to string
            enhanced_content = str(message)

            # Ensure proper line endings for HL7 v2.8 output (use \n for standard text files)
            if hasattr(self, 'original_line_ending'):
                # Use original line ending, but prefer \n for consistency
                line_ending = '\n'
            else:
                line_ending = '\n'

            # Replace \r with the desired line ending
            enhanced_content = enhanced_content.replace('\r', line_ending)

            # Write enhanced message
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(enhanced_content)

            self.logger.debug(f"Saved enhanced message: {output_path}")

        except Exception as e:
            raise HL7ProcessingError(
                f"Failed to save enhanced message: {e}",
                "FILE_WRITE_ERROR",
                str(original_path)
            )

    def quarantine_file(self, file_path: Path, error: HL7ProcessingError):
        """Move problematic file to quarantine with error details"""
        try:
            # Calculate relative path
            relative_path = file_path.relative_to(self.source_dir)
            quarantine_path = self.quarantine_dir / relative_path

            # Create parent directories
            quarantine_path.parent.mkdir(parents=True, exist_ok=True)

            # Copy file to quarantine
            shutil.copy2(file_path, quarantine_path)

            # Create error details file
            error_file = quarantine_path.with_suffix('.error.json')
            error_details = {
                'original_path': str(file_path),
                'error_code': error.error_code,
                'error_message': error.message,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }

            with open(error_file, 'w') as f:
                json.dump(error_details, f, indent=2)

            self.stats['files_quarantined'] += 1
            self.logger.info(f"Quarantined file: {quarantine_path}")

        except Exception as e:
            self.logger.error(f"Failed to quarantine file {file_path}: {e}")

    def process_file(self, file_path: Path) -> bool:
        """Process a single HL7 file"""
        try:
            self.logger.info(f"Processing: {file_path}")

            # Parse HL7 message
            message = self.parse_hl7_message(file_path)

            # Apply enhancements
            enhanced_message = self.apply_hl7_enhancements(message)

            # Save enhanced message
            self.save_enhanced_message(enhanced_message, file_path)

            # Track message type statistics
            if hasattr(self, 'current_message_type'):
                msg_type = self.current_message_type
                if msg_type in self.stats['message_types']:
                    self.stats['message_types'][msg_type] += 1
                else:
                    self.stats['message_types'][msg_type] = 1

            self.stats['files_enhanced'] += 1
            return True

        except HL7ProcessingError as e:
            self.stats['errors_encountered'] += 1

            # Log error
            self.log_error(e, {'stack': traceback.format_exc()})
            self.logger.error(f"Error processing {file_path}: {e.message}")

            # Quarantine file for certain error types
            if e.error_code in ['HL7_PARSING_FAILURE', 'PROCESSING_ERROR']:
                self.quarantine_file(file_path, e)

            return False

        except Exception as e:
            # Unexpected error
            error = HL7ProcessingError(
                f"Unexpected error: {e}",
                "PROCESSING_ERROR",
                str(file_path)
            )
            self.stats['errors_encountered'] += 1
            self.log_error(error, {'stack': traceback.format_exc()})
            self.logger.error(f"Unexpected error processing {file_path}: {e}")
            return False

    def run(self):
        """Main processing loop"""
        start_time = time.time()

        self.logger.info("🚀 Starting HL7 Message Enhancement Engine")

        try:
            # Find all HL7 files
            hl7_files = self.find_hl7_files()

            if not hl7_files:
                self.logger.warning(f"No HL7 files found in {self.source_dir}")
                return

            self.logger.info(f"Found {len(hl7_files)} HL7 files to process")

            # Process each file with progress tracking
            total_files = len(hl7_files)
            for i, file_path in enumerate(hl7_files, 1):
                self.stats['files_processed'] += 1

                # Show progress for large batches
                if total_files > 10:
                    progress_percent = (i / total_files) * 100
                    if i % max(1, total_files // 20) == 0 or i == total_files:  # Show progress every 5%
                        self.logger.info(f"Progress: {i}/{total_files} files ({progress_percent:.1f}%)")

                self.process_file(file_path)

            # Final statistics
            end_time = time.time()
            processing_time = end_time - start_time

            self.logger.info("✅ Processing completed!")
            self.logger.info(f"📊 Statistics:")
            self.logger.info(f"   Files processed: {self.stats['files_processed']}")
            self.logger.info(f"   Files enhanced: {self.stats['files_enhanced']}")
            self.logger.info(f"   Errors encountered: {self.stats['errors_encountered']}")
            self.logger.info(f"   Files quarantined: {self.stats['files_quarantined']}")
            self.logger.info(f"   Processing time: {processing_time:.2f} seconds")

            if self.stats['message_types']:
                self.logger.info("   Message types processed:")
                for msg_type, count in self.stats['message_types'].items():
                    self.logger.info(f"     {msg_type}: {count}")

            if self.stats['errors_encountered'] > 0:
                self.logger.info(f"Check error_details.log for detailed error analysis")

        except HL7ProcessingError as e:
            self.logger.error(f"Processing failed: {e.message}")
            self.log_error(e)
            sys.exit(1)
        except Exception as e:
            error = HL7ProcessingError(
                f"Unexpected system error: {e}",
                "PROCESSING_ERROR"
            )
            self.logger.error(f"System error: {e}")
            self.log_error(error, {'stack': traceback.format_exc()})
            sys.exit(1)
