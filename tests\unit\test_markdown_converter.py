"""
Unit tests for HL7 to Markdown converter
"""

import unittest
import tempfile
import shutil
import os
from pathlib import Path
from unittest.mock import patch, mock_open, MagicMock

# Import the module under test
import sys
sys.path.append('.')
sys.path.append('./src')
from src.hl7_enhance.converters.markdown_converter import HL7ToMarkdownConverter, ConversionConfig


class TestHL7ToMarkdownConverter(unittest.TestCase):
    """Test suite for HL7 to Markdown converter"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.input_folder = os.path.join(self.temp_dir, 'input')
        self.output_folder = os.path.join(self.temp_dir, 'output')
        os.makedirs(self.input_folder)
        os.makedirs(self.output_folder)
        
        # Sample HL7 content
        self.sample_hl7_1 = """MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A04|12345|P|2.8
EVN||200101010000
PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M||||||||||123456789
PV1|1|I|ICU^101^1|||||||||||||||V"""
        
        self.sample_hl7_2 = """MSH|^~\\&|SCHEDULING_APP|FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||SIU^S12|12346|P|2.8
SCH|1||12345|APPOINTMENT|||20240102100000|20240102110000|60|MIN|SCHEDULED||||||PROVIDER123
PID|1||987654321^^^MRN||SMITH^JANE^||19750615|F||||||||||987654321"""
        
        # Create converter with test configuration
        self.config = ConversionConfig(
            input_folder=self.input_folder,
            output_folder=self.output_folder,
            messages_per_file=2,
            output_filename_base="test_compiled_hl7_messages_part",
            remove_duplicates=True,
            include_source_info=True
        )
        self.converter = HL7ToMarkdownConverter(self.config)
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir)
    
    def create_test_file(self, filename: str, content: str):
        """Helper to create test HL7 files"""
        file_path = os.path.join(self.input_folder, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_converter_initialization(self):
        """Test converter initialization"""
        self.assertEqual(self.converter.config.input_folder, self.input_folder)
        self.assertEqual(self.converter.config.output_folder, self.output_folder)
        self.assertEqual(self.converter.config.messages_per_file, 2)
        self.assertTrue(self.converter.config.remove_duplicates)
    
    def test_validate_paths_success(self):
        """Test successful path validation"""
        result = self.converter.validate_paths()
        self.assertTrue(result)
        self.assertTrue(os.path.exists(self.output_folder))
    
    def test_validate_paths_input_not_exists(self):
        """Test path validation when input doesn't exist"""
        # Create converter with non-existent input
        config = ConversionConfig(
            input_folder="/non/existent/path",
            output_folder=self.output_folder
        )
        converter = HL7ToMarkdownConverter(config)
        
        result = converter.validate_paths()
        self.assertFalse(result)
    
    def test_is_hl7_file_by_extension(self):
        """Test HL7 file detection by extension"""
        # Test various extensions
        self.assertTrue(self.converter.is_hl7_file(Path("test.hl7")))
        self.assertTrue(self.converter.is_hl7_file(Path("test.txt")))
        self.assertTrue(self.converter.is_hl7_file(Path("test.msg")))
        self.assertFalse(self.converter.is_hl7_file(Path("test.pdf")))
    
    def test_is_hl7_file_by_content(self):
        """Test HL7 file detection by content"""
        # Create file with HL7 content but different extension
        hl7_file = self.create_test_file("test.dat", self.sample_hl7_1)
        self.assertTrue(self.converter.is_hl7_file(Path(hl7_file)))
        
        # Create file with non-HL7 content
        non_hl7_file = self.create_test_file("test.xyz", "This is not HL7 content")
        self.assertFalse(self.converter.is_hl7_file(Path(non_hl7_file)))
    
    def test_read_hl7_file_success(self):
        """Test successful HL7 file reading"""
        test_file = self.create_test_file("test.hl7", self.sample_hl7_1)
        
        content = self.converter.read_hl7_file(Path(test_file))
        
        self.assertIsNotNone(content)
        self.assertEqual(content, self.sample_hl7_1)
    
    def test_read_hl7_file_empty(self):
        """Test reading empty HL7 file"""
        test_file = self.create_test_file("empty.hl7", "")
        
        content = self.converter.read_hl7_file(Path(test_file))
        
        self.assertIsNone(content)
    
    def test_read_hl7_file_invalid(self):
        """Test reading invalid HL7 file"""
        test_file = self.create_test_file("invalid.hl7", "NOT_HL7_CONTENT")
        
        content = self.converter.read_hl7_file(Path(test_file))
        
        self.assertIsNone(content)
    
    def test_collect_unique_messages(self):
        """Test collecting unique HL7 messages"""
        # Create test files
        self.create_test_file("test1.hl7", self.sample_hl7_1)
        self.create_test_file("test2.hl7", self.sample_hl7_2)
        self.create_test_file("duplicate.hl7", self.sample_hl7_1)  # Duplicate
        
        messages = self.converter.collect_unique_messages()
        
        # Should have 2 unique messages (duplicate removed)
        self.assertEqual(len(messages), 2)
        
        # Check message structure
        for message in messages:
            self.assertIn('content', message)
            self.assertIn('filename', message)
            self.assertIn('relative_path', message)
    
    def test_collect_unique_messages_no_duplicates_removal(self):
        """Test collecting messages without duplicate removal"""
        # Configure to not remove duplicates
        self.converter.config.remove_duplicates = False
        
        # Create test files with duplicates
        self.create_test_file("test1.hl7", self.sample_hl7_1)
        self.create_test_file("test2.hl7", self.sample_hl7_2)
        self.create_test_file("duplicate.hl7", self.sample_hl7_1)  # Duplicate
        
        messages = self.converter.collect_unique_messages()
        
        # Should have 3 messages (duplicates not removed)
        self.assertEqual(len(messages), 3)
    
    def test_format_message_to_markdown(self):
        """Test formatting single message to markdown"""
        message_data = {
            'content': self.sample_hl7_1,
            'filename': 'test.hl7',
            'relative_path': 'test.hl7'
        }
        
        markdown = self.converter.format_message_to_markdown(message_data, 1)
        
        # Check markdown structure
        self.assertIn("## Message 1", markdown)
        self.assertIn("**Source File:** `test.hl7`", markdown)
        self.assertIn("**Path:** `test.hl7`", markdown)
        self.assertIn("```hl7", markdown)
        self.assertIn("MSH|", markdown)
        self.assertIn("```", markdown)
        self.assertIn("---", markdown)
    
    def test_format_message_to_markdown_no_source_info(self):
        """Test formatting message without source info"""
        self.converter.config.include_source_info = False
        
        message_data = {
            'content': self.sample_hl7_1,
            'filename': 'test.hl7',
            'relative_path': 'test.hl7'
        }
        
        markdown = self.converter.format_message_to_markdown(message_data, 1)
        
        # Should not include source info
        self.assertNotIn("**Source File:**", markdown)
        self.assertNotIn("**Path:**", markdown)
        self.assertIn("## Message 1", markdown)
        self.assertIn("```hl7", markdown)
    
    def test_create_markdown_files(self):
        """Test creating markdown files from messages"""
        messages = [
            {
                'content': self.sample_hl7_1,
                'filename': 'test1.hl7',
                'relative_path': 'test1.hl7'
            },
            {
                'content': self.sample_hl7_2,
                'filename': 'test2.hl7',
                'relative_path': 'test2.hl7'
            }
        ]
        
        files_created = self.converter.create_markdown_files(messages)
        
        # Should create 1 file (2 messages, batch size 2)
        self.assertEqual(files_created, 1)
        
        # Check that file was created
        expected_file = os.path.join(self.output_folder, "test_compiled_hl7_messages_part_1.md")
        self.assertTrue(os.path.exists(expected_file))
        
        # Check file content
        with open(expected_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn("# Compiled HL7 Messages (Part 1)", content)
        self.assertIn("## Message 1", content)
        self.assertIn("## Message 2", content)
        self.assertIn("MSH|", content)
    
    def test_create_markdown_files_multiple_batches(self):
        """Test creating multiple markdown files for large message sets"""
        # Create 5 messages with batch size 2
        messages = []
        for i in range(5):
            messages.append({
                'content': self.sample_hl7_1.replace("12345", f"1234{i}"),
                'filename': f'test{i}.hl7',
                'relative_path': f'test{i}.hl7'
            })
        
        files_created = self.converter.create_markdown_files(messages)
        
        # Should create 3 files (2+2+1 messages)
        self.assertEqual(files_created, 3)
        
        # Check that all files were created
        for i in range(1, 4):
            expected_file = os.path.join(self.output_folder, f"test_compiled_hl7_messages_part_{i}.md")
            self.assertTrue(os.path.exists(expected_file))
    
    def test_convert_batch_success(self):
        """Test successful batch conversion"""
        # Create test files
        self.create_test_file("test1.hl7", self.sample_hl7_1)
        self.create_test_file("test2.hl7", self.sample_hl7_2)
        
        result = self.converter.convert_batch()
        
        # Check result
        self.assertEqual(result.unique_messages_found, 2)
        self.assertEqual(result.markdown_files_created, 1)
        self.assertEqual(len(result.errors_encountered), 0)
        
        # Check that markdown file was created
        output_files = list(Path(self.output_folder).glob("*.md"))
        self.assertEqual(len(output_files), 1)
    
    def test_convert_batch_no_files(self):
        """Test batch conversion with no HL7 files"""
        result = self.converter.convert_batch()
        
        # Should handle gracefully
        self.assertEqual(result.unique_messages_found, 0)
        self.assertEqual(result.markdown_files_created, 0)
        self.assertIn("No HL7 messages found", result.errors_encountered)
    
    def test_convert_single_file(self):
        """Test converting single file"""
        input_file = self.create_test_file("single.hl7", self.sample_hl7_1)
        output_file = os.path.join(self.output_folder, "single.md")
        
        result = self.converter.convert_single_file(input_file, output_file)
        
        self.assertTrue(result)
        self.assertTrue(os.path.exists(output_file))
        
        # Check content
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn("# HL7 Message: single.hl7", content)
        self.assertIn("MSH|", content)
    
    def test_convert_single_file_invalid(self):
        """Test converting invalid single file"""
        input_file = self.create_test_file("invalid.hl7", "INVALID_CONTENT")
        output_file = os.path.join(self.output_folder, "invalid.md")
        
        result = self.converter.convert_single_file(input_file, output_file)
        
        self.assertFalse(result)
        self.assertFalse(os.path.exists(output_file))


if __name__ == '__main__':
    unittest.main()
