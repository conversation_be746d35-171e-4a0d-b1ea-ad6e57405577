"""
Enhanced Pipeline Logger for HL7 processing
"""

import json
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class LogEntry:
    """Structured log entry for pipeline events"""
    timestamp: str
    level: str
    component: str
    event_type: str
    message: str
    file_path: Optional[str] = None
    processing_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None


class PipelineLogger:
    """Enhanced logger for HL7 pipeline with structured logging and metrics"""
    
    def __init__(self, log_directory: str = "logs", log_level: str = "INFO"):
        """Initialize the pipeline logger"""
        self.log_dir = Path(log_directory)
        self.log_dir.mkdir(exist_ok=True)
        
        self.log_level = getattr(logging, log_level.upper())
        
        # Setup different log files
        self.activity_log = self.log_dir / "pipeline_activity.log"
        self.error_log = self.log_dir / "pipeline_errors.log"
        self.metrics_log = self.log_dir / "pipeline_metrics.jsonl"
        
        # Setup loggers
        self.activity_logger = self._setup_activity_logger()
        self.error_logger = self._setup_error_logger()
        self.metrics_logger = self._setup_metrics_logger()
        
        # Performance tracking
        self.performance_metrics = {
            'files_processed': 0,
            'total_processing_time': 0.0,
            'validation_time': 0.0,
            'enhancement_time': 0.0,
            'errors_encountered': 0,
            'start_time': None
        }
    
    def _setup_activity_logger(self) -> logging.Logger:
        """Setup activity logger for general pipeline events"""
        logger = logging.getLogger('pipeline_activity')
        logger.setLevel(self.log_level)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            self.activity_log,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(self.log_level)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _setup_error_logger(self) -> logging.Logger:
        """Setup error logger for detailed error tracking"""
        logger = logging.getLogger('pipeline_errors')
        logger.setLevel(logging.ERROR)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler
        file_handler = logging.FileHandler(self.error_log)
        file_handler.setLevel(logging.ERROR)
        
        # JSON formatter for structured error logging
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        
        return logger
    
    def _setup_metrics_logger(self) -> logging.Logger:
        """Setup metrics logger for performance data"""
        logger = logging.getLogger('pipeline_metrics')
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler for JSONL format
        file_handler = logging.FileHandler(self.metrics_log)
        file_handler.setLevel(logging.INFO)
        
        # No formatter - we'll write JSON directly
        logger.addHandler(file_handler)
        
        return logger
    
    def start_pipeline(self, config: Dict[str, Any]):
        """Log pipeline start event"""
        self.performance_metrics['start_time'] = datetime.utcnow()
        
        self.activity_logger.info("🚀 Starting HL7 Enhancement Pipeline")
        self.activity_logger.info(f"Configuration: {json.dumps(config, indent=2)}")
        
        # Log structured metrics
        self._log_metrics({
            'event_type': 'pipeline_start',
            'timestamp': self.performance_metrics['start_time'].isoformat() + 'Z',
            'configuration': config
        })
    
    def log_file_processing_start(self, file_path: str):
        """Log start of file processing"""
        self.activity_logger.debug(f"Processing file: {file_path}")
        
        self._log_metrics({
            'event_type': 'file_processing_start',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'file_path': file_path
        })
    
    def log_file_processing_complete(
        self, 
        file_path: str, 
        success: bool, 
        processing_time: float,
        was_enhanced: bool = False,
        enhancement_actions: int = 0
    ):
        """Log completion of file processing"""
        status = "SUCCESS" if success else "FAILED"
        enhancement_info = f" (Enhanced: {was_enhanced}, Actions: {enhancement_actions})" if was_enhanced else ""
        
        self.activity_logger.info(
            f"[{status}] {file_path} - {processing_time:.3f}s{enhancement_info}"
        )
        
        # Update performance metrics
        self.performance_metrics['files_processed'] += 1
        self.performance_metrics['total_processing_time'] += processing_time
        
        if not success:
            self.performance_metrics['errors_encountered'] += 1
        
        # Log structured metrics
        self._log_metrics({
            'event_type': 'file_processing_complete',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'file_path': file_path,
            'success': success,
            'processing_time': processing_time,
            'was_enhanced': was_enhanced,
            'enhancement_actions': enhancement_actions
        })
    
    def log_validation_result(self, file_path: str, validation_time: float, is_valid: bool, error_count: int):
        """Log validation results"""
        self.performance_metrics['validation_time'] += validation_time
        
        status = "VALID" if is_valid else f"INVALID ({error_count} errors)"
        self.activity_logger.debug(f"Validation [{status}] {file_path} - {validation_time:.3f}s")
        
        self._log_metrics({
            'event_type': 'validation_complete',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'file_path': file_path,
            'validation_time': validation_time,
            'is_valid': is_valid,
            'error_count': error_count
        })
    
    def log_enhancement_result(
        self, 
        file_path: str, 
        enhancement_time: float, 
        success: bool, 
        actions_performed: int
    ):
        """Log enhancement results"""
        self.performance_metrics['enhancement_time'] += enhancement_time
        
        status = "SUCCESS" if success else "FAILED"
        self.activity_logger.debug(
            f"Enhancement [{status}] {file_path} - {enhancement_time:.3f}s ({actions_performed} actions)"
        )
        
        self._log_metrics({
            'event_type': 'enhancement_complete',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'file_path': file_path,
            'enhancement_time': enhancement_time,
            'success': success,
            'actions_performed': actions_performed
        })
    
    def log_error(self, component: str, error_message: str, file_path: str = None, metadata: Dict[str, Any] = None):
        """Log detailed error information"""
        error_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'component': component,
            'error_message': error_message,
            'file_path': file_path,
            'metadata': metadata or {}
        }
        
        self.error_logger.error(json.dumps(error_entry))
        self.activity_logger.error(f"[{component}] {error_message}" + (f" - {file_path}" if file_path else ""))
    
    def log_quarantine(self, file_path: str, reason: str, retry_count: int = 0):
        """Log file quarantine event"""
        self.activity_logger.warning(f"QUARANTINED: {file_path} - {reason} (Retry: {retry_count})")
        
        self._log_metrics({
            'event_type': 'file_quarantined',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'file_path': file_path,
            'reason': reason,
            'retry_count': retry_count
        })
    
    def log_progress(self, current: int, total: int, batch_info: str = ""):
        """Log processing progress"""
        percentage = (current / total) * 100 if total > 0 else 0
        
        self.activity_logger.info(
            f"Progress: {current}/{total} ({percentage:.1f}%){' - ' + batch_info if batch_info else ''}"
        )
    
    def complete_pipeline(self) -> Dict[str, Any]:
        """Log pipeline completion and return final metrics"""
        end_time = datetime.utcnow()
        total_time = (end_time - self.performance_metrics['start_time']).total_seconds()
        
        # Calculate final metrics
        final_metrics = {
            'total_execution_time': total_time,
            'files_processed': self.performance_metrics['files_processed'],
            'average_processing_time': (
                self.performance_metrics['total_processing_time'] / 
                max(self.performance_metrics['files_processed'], 1)
            ),
            'files_per_second': (
                self.performance_metrics['files_processed'] / max(total_time, 1)
            ),
            'validation_time_percentage': (
                self.performance_metrics['validation_time'] / 
                max(self.performance_metrics['total_processing_time'], 1) * 100
            ),
            'enhancement_time_percentage': (
                self.performance_metrics['enhancement_time'] / 
                max(self.performance_metrics['total_processing_time'], 1) * 100
            ),
            'error_rate': (
                self.performance_metrics['errors_encountered'] / 
                max(self.performance_metrics['files_processed'], 1) * 100
            )
        }
        
        self.activity_logger.info("✅ Pipeline completed successfully")
        self.activity_logger.info(f"📊 Final Statistics:")
        self.activity_logger.info(f"   Total execution time: {total_time:.2f} seconds")
        self.activity_logger.info(f"   Files processed: {self.performance_metrics['files_processed']}")
        self.activity_logger.info(f"   Average processing time: {final_metrics['average_processing_time']:.3f}s per file")
        self.activity_logger.info(f"   Processing rate: {final_metrics['files_per_second']:.2f} files/second")
        self.activity_logger.info(f"   Error rate: {final_metrics['error_rate']:.2f}%")
        
        # Log final metrics
        self._log_metrics({
            'event_type': 'pipeline_complete',
            'timestamp': end_time.isoformat() + 'Z',
            'final_metrics': final_metrics
        })
        
        return final_metrics
    
    def _log_metrics(self, metrics_data: Dict[str, Any]):
        """Log structured metrics data"""
        # Write JSON line to metrics file
        metrics_json = json.dumps(metrics_data)
        
        # Use the metrics logger to write directly to file
        for handler in self.metrics_logger.handlers:
            if isinstance(handler, logging.FileHandler):
                handler.stream.write(metrics_json + '\n')
                handler.stream.flush()
