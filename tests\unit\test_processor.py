"""
Unit tests for HL7 Processor - Main HL7 processing engine
"""

import unittest
import tempfile
import shutil
import os
import json
from pathlib import Path
from unittest.mock import patch, MagicMock, mock_open
import hl7

# Import the module under test
import sys
sys.path.append('.')
sys.path.append('./src')
from src.hl7_enhance.core.processor import HL7Processor
from src.hl7_enhance.core.exceptions import HL7ProcessingError


class TestHL7Processor(unittest.TestCase):
    """Test suite for HL7Processor class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.source_dir = os.path.join(self.temp_dir, 'source')
        self.output_dir = os.path.join(self.temp_dir, 'output')
        os.makedirs(self.source_dir)
        os.makedirs(self.output_dir)
        
        # Create test HL7 content
        self.valid_hl7_content = """MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A04|12345|P|2.8
EVN||200101010000
PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M||||||||||123456789
PV1|1|I|ICU^101^1|||||||||||||||V"""
        
        self.invalid_hl7_content = """INVALID_SEGMENT|data|more_data
PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M||||||||||123456789"""
        
        # Create processor instance
        self.processor = HL7Processor(self.source_dir, self.output_dir, verbose=True)
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir)
    
    def test_processor_initialization(self):
        """Test processor initialization"""
        self.assertEqual(str(self.processor.source_dir), self.source_dir)
        self.assertEqual(str(self.processor.output_dir), self.output_dir)
        self.assertTrue(self.processor.verbose)
        self.assertIsInstance(self.processor.stats, dict)
        self.assertEqual(self.processor.stats['files_processed'], 0)
    
    def test_create_directories(self):
        """Test directory creation"""
        # Directories should be created during initialization
        self.assertTrue(os.path.exists(self.output_dir))
        self.assertTrue(os.path.exists(self.processor.quarantine_dir))
    
    def test_find_hl7_files(self):
        """Test finding HL7 files"""
        # Create test files
        test_file1 = os.path.join(self.source_dir, 'test1.hl7')
        test_file2 = os.path.join(self.source_dir, 'test2.txt')
        test_file3 = os.path.join(self.source_dir, 'test3.hl7')
        
        with open(test_file1, 'w') as f:
            f.write(self.valid_hl7_content)
        with open(test_file2, 'w') as f:
            f.write("not an hl7 file")
        with open(test_file3, 'w') as f:
            f.write(self.valid_hl7_content)
        
        # Find HL7 files
        hl7_files = self.processor.find_hl7_files()
        
        # Should find 2 .hl7 files
        self.assertEqual(len(hl7_files), 2)
        file_names = [f.name for f in hl7_files]
        self.assertIn('test1.hl7', file_names)
        self.assertIn('test3.hl7', file_names)
    
    def test_parse_valid_hl7_message(self):
        """Test parsing valid HL7 message"""
        test_file = os.path.join(self.source_dir, 'valid.hl7')
        with open(test_file, 'w') as f:
            f.write(self.valid_hl7_content)
        
        message = self.processor.parse_hl7_message(Path(test_file))
        
        self.assertIsInstance(message, hl7.Message)
        msh = message.segment('MSH')
        self.assertEqual(str(msh[3]), 'SENDING_APP')
        self.assertEqual(str(msh[9]), 'ADT^A04')
    
    def test_parse_invalid_hl7_message(self):
        """Test parsing invalid HL7 message"""
        test_file = os.path.join(self.source_dir, 'invalid.hl7')
        with open(test_file, 'w') as f:
            f.write(self.invalid_hl7_content)
        
        with self.assertRaises(HL7ProcessingError) as context:
            self.processor.parse_hl7_message(Path(test_file))
        
        self.assertEqual(context.exception.error_code, "HL7_PARSING_FAILURE")
    
    def test_parse_empty_file(self):
        """Test parsing empty file"""
        test_file = os.path.join(self.source_dir, 'empty.hl7')
        with open(test_file, 'w') as f:
            f.write("")
        
        with self.assertRaises(HL7ProcessingError) as context:
            self.processor.parse_hl7_message(Path(test_file))
        
        self.assertEqual(context.exception.error_code, "HL7_PARSING_FAILURE")
        self.assertIn("Empty file", context.exception.message)
    
    def test_apply_hl7_enhancements(self):
        """Test applying HL7 enhancements"""
        test_file = os.path.join(self.source_dir, 'test.hl7')
        with open(test_file, 'w') as f:
            f.write(self.valid_hl7_content)
        
        message = self.processor.parse_hl7_message(Path(test_file))
        enhanced_message = self.processor.apply_hl7_enhancements(message)
        
        # Check that version was updated to 2.8
        msh = enhanced_message.segment('MSH')
        self.assertEqual(str(msh[12]), '2.8')
        
        # Check that processing ID was set to Production
        self.assertEqual(str(msh[11]), 'P')
    
    def test_save_enhanced_message(self):
        """Test saving enhanced message"""
        test_file = os.path.join(self.source_dir, 'test.hl7')
        with open(test_file, 'w') as f:
            f.write(self.valid_hl7_content)
        
        message = self.processor.parse_hl7_message(Path(test_file))
        enhanced_message = self.processor.apply_hl7_enhancements(message)
        
        # Save enhanced message
        self.processor.save_enhanced_message(enhanced_message, Path(test_file))
        
        # Check that file was saved
        output_file = os.path.join(self.output_dir, 'test.hl7')
        self.assertTrue(os.path.exists(output_file))
        
        # Check content
        with open(output_file, 'r') as f:
            content = f.read()
        
        self.assertIn('MSH', content)
        self.assertIn('2.8', content)  # Version should be updated
    
    def test_quarantine_file(self):
        """Test quarantining problematic file"""
        test_file = os.path.join(self.source_dir, 'problem.hl7')
        with open(test_file, 'w') as f:
            f.write(self.invalid_hl7_content)
        
        error = HL7ProcessingError(
            "Test error",
            "TEST_ERROR",
            test_file
        )
        
        # Quarantine the file
        self.processor.quarantine_file(Path(test_file), error)
        
        # Check that file was quarantined
        quarantine_file = self.processor.quarantine_dir / 'problem.hl7'
        self.assertTrue(quarantine_file.exists())
        
        # Check that error details file was created
        error_file = quarantine_file.with_suffix('.error.json')
        self.assertTrue(error_file.exists())
        
        # Check error details content
        with open(error_file, 'r') as f:
            error_details = json.load(f)
        
        self.assertEqual(error_details['error_code'], 'TEST_ERROR')
        self.assertEqual(error_details['error_message'], 'Test error')
    
    def test_process_file_success(self):
        """Test successful file processing"""
        test_file = os.path.join(self.source_dir, 'success.hl7')
        with open(test_file, 'w') as f:
            f.write(self.valid_hl7_content)
        
        result = self.processor.process_file(Path(test_file))
        
        self.assertTrue(result)
        self.assertEqual(self.processor.stats['files_enhanced'], 1)
        
        # Check that output file exists
        output_file = os.path.join(self.output_dir, 'success.hl7')
        self.assertTrue(os.path.exists(output_file))
    
    def test_process_file_failure(self):
        """Test file processing failure"""
        test_file = os.path.join(self.source_dir, 'failure.hl7')
        with open(test_file, 'w') as f:
            f.write(self.invalid_hl7_content)
        
        result = self.processor.process_file(Path(test_file))
        
        self.assertFalse(result)
        self.assertEqual(self.processor.stats['errors_encountered'], 1)
        
        # Check that file was quarantined
        quarantine_file = self.processor.quarantine_dir / 'failure.hl7'
        self.assertTrue(quarantine_file.exists())
    
    @patch('src.hl7_enhance.core.processor.json.load')
    def test_load_config_success(self, mock_json_load):
        """Test successful config loading"""
        mock_json_load.return_value = [{'test': 'rule'}]
        
        # Create a new processor to test config loading
        processor = HL7Processor(self.source_dir, self.output_dir)
        
        # Config should be loaded (mocked)
        self.assertEqual(processor.mapping_rules, [{'test': 'rule'}])
    
    def test_load_config_file_not_found(self):
        """Test config loading when file doesn't exist"""
        # Remove any existing config files
        config_files = ['config/mapping_rules.json', 'mapping_rules.json']
        for config_file in config_files:
            if os.path.exists(config_file):
                os.remove(config_file)
        
        # Create a new processor
        processor = HL7Processor(self.source_dir, self.output_dir)
        
        # Should have empty mapping rules
        self.assertEqual(processor.mapping_rules, [])
    
    def test_statistics_tracking(self):
        """Test statistics tracking during processing"""
        # Create multiple test files
        for i in range(3):
            test_file = os.path.join(self.source_dir, f'test{i}.hl7')
            with open(test_file, 'w') as f:
                f.write(self.valid_hl7_content)
        
        # Create one invalid file
        invalid_file = os.path.join(self.source_dir, 'invalid.hl7')
        with open(invalid_file, 'w') as f:
            f.write(self.invalid_hl7_content)
        
        # Process all files
        hl7_files = self.processor.find_hl7_files()
        for file_path in hl7_files:
            self.processor.process_file(file_path)
        
        # Check statistics
        self.assertEqual(self.processor.stats['files_processed'], 4)
        self.assertEqual(self.processor.stats['files_enhanced'], 3)
        self.assertEqual(self.processor.stats['errors_encountered'], 1)
        self.assertEqual(self.processor.stats['files_quarantined'], 1)


if __name__ == '__main__':
    unittest.main()
