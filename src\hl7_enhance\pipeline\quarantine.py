"""
Quarantine Manager for HL7 messages that fail validation or enhancement
"""

import json
import shutil
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from ..validators.enhanced_validator import ValidationResult


@dataclass
class QuarantineRecord:
    """Record of a quarantined HL7 message"""
    file_path: str
    quarantine_path: str
    quarantine_timestamp: str
    validation_errors: List[str]
    enhancement_errors: List[str]
    retry_count: int = 0
    max_retries: int = 3
    last_retry_timestamp: Optional[str] = None
    resolution_status: str = "QUARANTINED"  # QUARANTINED, RESOLVED, ABANDONED
    resolution_timestamp: Optional[str] = None
    resolution_notes: Optional[str] = None


class QuarantineManager:
    """Manages quarantined HL7 messages with retry and resolution tracking"""
    
    def __init__(self, quarantine_directory: str, config: Dict[str, Any] = None):
        """Initialize the quarantine manager"""
        self.quarantine_dir = Path(quarantine_directory)
        self.quarantine_dir.mkdir(parents=True, exist_ok=True)
        
        self.config = config or {}
        self.max_retry_attempts = self.config.get('max_retry_attempts', 3)
        self.auto_retry_enabled = self.config.get('auto_retry_enabled', True)
        self.retry_delay_hours = self.config.get('retry_delay_hours', 24)
        
        self.logger = logging.getLogger(__name__)
        
        # Quarantine database file
        self.db_file = self.quarantine_dir / "quarantine_database.json"
        self.records = self._load_quarantine_database()
    
    def _load_quarantine_database(self) -> Dict[str, QuarantineRecord]:
        """Load quarantine database from file"""
        if not self.db_file.exists():
            return {}
        
        try:
            with open(self.db_file, 'r') as f:
                data = json.load(f)
            
            # Convert dict data back to QuarantineRecord objects
            records = {}
            for file_path, record_data in data.items():
                records[file_path] = QuarantineRecord(**record_data)
            
            self.logger.info(f"Loaded {len(records)} quarantine records")
            return records
            
        except Exception as e:
            self.logger.error(f"Failed to load quarantine database: {e}")
            return {}
    
    def _save_quarantine_database(self):
        """Save quarantine database to file"""
        try:
            # Convert QuarantineRecord objects to dict for JSON serialization
            data = {}
            for file_path, record in self.records.items():
                data[file_path] = asdict(record)
            
            with open(self.db_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to save quarantine database: {e}")
    
    def quarantine_message(
        self, 
        file_path: str, 
        validation_result: ValidationResult,
        retry_count: int = 0,
        enhancement_errors: List[str] = None
    ) -> QuarantineRecord:
        """Quarantine a message that failed validation or enhancement"""
        
        # Create quarantine subdirectory structure
        source_path = Path(file_path)
        relative_path = source_path.name  # Simplified for now
        quarantine_path = self.quarantine_dir / relative_path
        
        # Ensure unique quarantine path
        counter = 1
        original_quarantine_path = quarantine_path
        while quarantine_path.exists():
            stem = original_quarantine_path.stem
            suffix = original_quarantine_path.suffix
            quarantine_path = original_quarantine_path.parent / f"{stem}_{counter}{suffix}"
            counter += 1
        
        try:
            # Copy file to quarantine
            shutil.copy2(file_path, quarantine_path)
            
            # Extract validation errors
            validation_errors = [
                f"{issue.code}: {issue.message}" 
                for issue in validation_result.issues
            ]
            
            # Create quarantine record
            record = QuarantineRecord(
                file_path=str(source_path),
                quarantine_path=str(quarantine_path),
                quarantine_timestamp=datetime.utcnow().isoformat() + 'Z',
                validation_errors=validation_errors,
                enhancement_errors=enhancement_errors or [],
                retry_count=retry_count,
                max_retries=self.max_retry_attempts
            )
            
            # Save record
            self.records[str(source_path)] = record
            self._save_quarantine_database()
            
            # Create detailed error report
            self._create_error_report(quarantine_path, record, validation_result)
            
            self.logger.info(f"Quarantined message: {file_path} -> {quarantine_path}")
            return record
            
        except Exception as e:
            self.logger.error(f"Failed to quarantine message {file_path}: {e}")
            raise
    
    def _create_error_report(
        self, 
        quarantine_path: Path, 
        record: QuarantineRecord, 
        validation_result: ValidationResult
    ):
        """Create detailed error report for quarantined message"""
        report_path = quarantine_path.with_suffix('.error_report.json')
        
        report = {
            'quarantine_record': asdict(record),
            'validation_details': {
                'is_valid': validation_result.is_valid,
                'message_type': validation_result.message_type,
                'version': validation_result.version,
                'total_issues': len(validation_result.issues),
                'error_count': len(validation_result.get_errors()),
                'warning_count': len(validation_result.get_warnings()),
                'issues': [
                    {
                        'severity': issue.severity.value,
                        'code': issue.code,
                        'message': issue.message,
                        'segment': issue.segment,
                        'field': issue.field,
                        'location': issue.location,
                        'fix_suggestion': issue.fix_suggestion
                    } for issue in validation_result.issues
                ]
            },
            'statistics': validation_result.statistics
        }
        
        try:
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to create error report: {e}")
    
    def get_retry_eligible_messages(self) -> List[QuarantineRecord]:
        """Get messages eligible for retry based on configuration"""
        if not self.auto_retry_enabled:
            return []
        
        eligible = []
        current_time = datetime.utcnow()
        
        for record in self.records.values():
            if (record.resolution_status == "QUARANTINED" and 
                record.retry_count < record.max_retries):
                
                # Check if enough time has passed since last retry
                if record.last_retry_timestamp:
                    last_retry = datetime.fromisoformat(record.last_retry_timestamp.replace('Z', '+00:00'))
                    if current_time - last_retry < timedelta(hours=self.retry_delay_hours):
                        continue
                
                eligible.append(record)
        
        return eligible
    
    def update_retry_attempt(self, record: QuarantineRecord, success: bool):
        """Update retry attempt information"""
        record.retry_count += 1
        record.last_retry_timestamp = datetime.utcnow().isoformat() + 'Z'
        
        if success:
            record.resolution_status = "RESOLVED"
            record.resolution_timestamp = datetime.utcnow().isoformat() + 'Z'
            record.resolution_notes = f"Successfully processed on retry attempt {record.retry_count}"
            self.logger.info(f"Message resolved on retry: {record.file_path}")
        elif record.retry_count >= record.max_retries:
            record.resolution_status = "ABANDONED"
            record.resolution_timestamp = datetime.utcnow().isoformat() + 'Z'
            record.resolution_notes = f"Abandoned after {record.retry_count} retry attempts"
            self.logger.warning(f"Message abandoned after max retries: {record.file_path}")
        
        self._save_quarantine_database()
    
    def get_quarantine_statistics(self) -> Dict[str, Any]:
        """Get comprehensive quarantine statistics"""
        total_quarantined = len(self.records)
        resolved = sum(1 for r in self.records.values() if r.resolution_status == "RESOLVED")
        abandoned = sum(1 for r in self.records.values() if r.resolution_status == "ABANDONED")
        active = total_quarantined - resolved - abandoned
        
        # Error type analysis
        error_types = {}
        for record in self.records.values():
            for error in record.validation_errors:
                error_code = error.split(':')[0] if ':' in error else error
                error_types[error_code] = error_types.get(error_code, 0) + 1
        
        return {
            'total_quarantined': total_quarantined,
            'active_quarantined': active,
            'resolved': resolved,
            'abandoned': abandoned,
            'resolution_rate': (resolved / total_quarantined * 100) if total_quarantined > 0 else 0,
            'common_error_types': dict(sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:10]),
            'retry_eligible': len(self.get_retry_eligible_messages())
        }
    
    def generate_quarantine_report(self) -> Dict[str, Any]:
        """Generate comprehensive quarantine report"""
        stats = self.get_quarantine_statistics()
        
        return {
            'report_timestamp': datetime.utcnow().isoformat() + 'Z',
            'quarantine_directory': str(self.quarantine_dir),
            'configuration': self.config,
            'statistics': stats,
            'detailed_records': [asdict(record) for record in self.records.values()]
        }
