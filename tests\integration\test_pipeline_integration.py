#!/usr/bin/env python3
"""
Integration tests for the HL7 Validation and Enhancement Pipeline
Tests the complete pipeline functionality including edge cases and performance
"""

import unittest
import tempfile
import shutil
import json
import time
from pathlib import Path
from unittest.mock import patch, MagicMock

# Import pipeline components
import sys
sys.path.append('.')
sys.path.append('./src')

from src.hl7_enhance.validators.enhanced_validator import EnhancedHL7Validator, ValidationSeverity
from src.hl7_enhance.pipeline.orchestrator import PipelineOrchestrator, PipelineConfig
from src.hl7_enhance.pipeline.quarantine import QuarantineManager
from src.hl7_enhance.pipeline.logger import PipelineLogger

class TestPipelineIntegration(unittest.TestCase):
    """Integration tests for the complete HL7 pipeline"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.source_dir = self.test_dir / "source"
        self.output_dir = self.test_dir / "output"
        self.quarantine_dir = self.test_dir / "quarantine"
        
        # Create directories
        self.source_dir.mkdir(parents=True)
        self.output_dir.mkdir(parents=True)
        self.quarantine_dir.mkdir(parents=True)
        
        # Sample HL7 messages for testing
        self.valid_adt_message = """MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A04|12345|P|2.8
EVN||200101010000
PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M||||||||||123456789
PV1|1|I|ICU^101^1|||||||||||||||V"""
        
        self.invalid_adt_message = """MSH|^~\\&|SENDING_APP|SENDING_FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||ADT^A04|12345|P|2.5
PID|1||123456789^^^MRN||DOE^JOHN^MIDDLE||19800101|M||||||||||123456789
PV1|1|I|ICU^101^1|||||||||||||||V"""
        
        self.siu_message = """MSH|^~\\&|SCHEDULING_APP|FACILITY|RECEIVING_APP|RECEIVING_FACILITY|20240101120000||SIU^S12|12346|P|2.8
SCH|1||12345|APPOINTMENT|||20240102100000|20240102110000|60|MIN|SCHEDULED||||||PROVIDER123
PID|1||987654321^^^MRN||SMITH^JANE^||19750615|F||||||||||987654321"""
        
        # Pipeline configuration
        self.config = PipelineConfig(
            source_directory=str(self.source_dir),
            output_directory=str(self.output_dir),
            quarantine_directory=str(self.quarantine_dir),
            file_patterns=["*.hl7"],
            max_enhancement_retries=2,
            enable_auto_retry=True,
            batch_size=10,
            parallel_processing=False,
            backup_original_files=True,
            generate_reports=True,
            log_level="DEBUG"
        )
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir)
    
    def create_test_file(self, filename: str, content: str):
        """Helper to create test HL7 files"""
        file_path = self.source_dir / filename
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_pipeline_with_valid_messages(self):
        """Test pipeline processing with valid HL7 messages"""
        # Create test files
        self.create_test_file("valid_adt.hl7", self.valid_adt_message)
        self.create_test_file("valid_siu.hl7", self.siu_message)
        
        # Run pipeline
        orchestrator = PipelineOrchestrator(self.config)
        result = orchestrator.run_pipeline()
        
        # Verify results
        self.assertIsInstance(result, dict)
        self.assertIn('execution_summary', result)
        
        # Check that files were processed
        stats = result['execution_summary']
        self.assertEqual(stats['total_files_processed'], 2)
        self.assertGreaterEqual(stats['files_successfully_enhanced'] + stats['files_already_valid'], 1)
        
        # Check output files exist
        output_files = list(self.output_dir.rglob("*.hl7"))
        self.assertGreater(len(output_files), 0)
    
    def test_pipeline_with_invalid_messages(self):
        """Test pipeline processing with invalid HL7 messages"""
        # Create test files with validation issues
        self.create_test_file("invalid_version.hl7", self.invalid_adt_message)
        self.create_test_file("malformed.hl7", "INVALID|CONTENT|HERE")
        
        # Run pipeline
        orchestrator = PipelineOrchestrator(self.config)
        result = orchestrator.run_pipeline()
        
        # Verify results
        stats = result['execution_summary']
        self.assertEqual(stats['total_files_processed'], 2)
        
        # Some files should be quarantined or processed
        self.assertGreaterEqual(stats['files_quarantined'] + stats['files_successfully_enhanced'], 1)
    
    def test_pipeline_mixed_message_types(self):
        """Test pipeline with mixed valid and invalid messages"""
        # Create mix of valid and invalid files
        self.create_test_file("valid_adt.hl7", self.valid_adt_message)
        self.create_test_file("valid_siu.hl7", self.siu_message)
        self.create_test_file("invalid_version.hl7", self.invalid_adt_message)
        self.create_test_file("empty.hl7", "")
        
        # Run pipeline
        orchestrator = PipelineOrchestrator(self.config)
        result = orchestrator.run_pipeline()
        
        # Verify all files were processed
        stats = result['execution_summary']
        self.assertEqual(stats['total_files_processed'], 4)
        
        # Should have some successful and some quarantined
        total_handled = (stats['files_successfully_enhanced'] + 
                        stats['files_already_valid'] + 
                        stats['files_quarantined'])
        self.assertEqual(total_handled, 4)
    
    def test_quarantine_manager_integration(self):
        """Test quarantine manager functionality"""
        # Create invalid file
        invalid_file = self.create_test_file("invalid.hl7", "INVALID_CONTENT")
        
        # Initialize quarantine manager
        quarantine_manager = QuarantineManager(str(self.quarantine_dir))
        
        # Validate the invalid message
        validator = EnhancedHL7Validator()
        validation_result = validator.validate_file(str(invalid_file))
        
        # Quarantine the message
        record = quarantine_manager.quarantine_message(
            str(invalid_file),
            validation_result,
            retry_count=0
        )
        
        # Verify quarantine record
        self.assertIsNotNone(record)
        self.assertEqual(record.file_path, str(invalid_file))
        self.assertGreater(len(record.validation_errors), 0)
        
        # Check quarantine file exists
        quarantine_path = Path(record.quarantine_path)
        self.assertTrue(quarantine_path.exists())
        
        # Check error report exists
        error_report_path = quarantine_path.with_suffix('.error_report.json')
        self.assertTrue(error_report_path.exists())
    
    def test_validator_integration(self):
        """Test enhanced validator integration"""
        validator = EnhancedHL7Validator()
        
        # Test valid message
        valid_result = validator.validate_message(self.valid_adt_message)
        self.assertTrue(valid_result.is_valid)
        self.assertEqual(valid_result.message_type, "ADT")
        self.assertEqual(valid_result.version, "2.8")
        
        # Test invalid message
        invalid_result = validator.validate_message(self.invalid_adt_message)
        self.assertFalse(invalid_result.is_valid)
        self.assertGreater(len(invalid_result.issues), 0)
    
    def test_performance_metrics(self):
        """Test performance metrics collection"""
        # Create multiple test files
        for i in range(5):
            self.create_test_file(f"test_{i}.hl7", self.valid_adt_message)
        
        # Run pipeline with timing
        start_time = time.time()
        orchestrator = PipelineOrchestrator(self.config)
        result = orchestrator.run_pipeline()
        end_time = time.time()
        
        # Verify performance metrics
        self.assertIn('performance_metrics', result)
        metrics = result['performance_metrics']
        
        self.assertIn('average_processing_time_per_file', metrics)
        self.assertIn('files_per_second', metrics)
        self.assertIn('success_rate_percentage', metrics)
        
        # Verify timing is reasonable
        total_time = end_time - start_time
        self.assertLess(metrics['average_processing_time_per_file'], total_time)
    
    def test_batch_processing(self):
        """Test batch processing functionality"""
        # Create more files than batch size
        batch_size = 3
        total_files = 7
        
        for i in range(total_files):
            self.create_test_file(f"batch_test_{i}.hl7", self.valid_adt_message)
        
        # Configure smaller batch size
        config = self.config
        config.batch_size = batch_size
        
        # Run pipeline
        orchestrator = PipelineOrchestrator(config)
        result = orchestrator.run_pipeline()
        
        # Verify all files were processed
        stats = result['execution_summary']
        self.assertEqual(stats['total_files_processed'], total_files)
    
    def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms"""
        # Create files with various error conditions
        self.create_test_file("valid.hl7", self.valid_adt_message)
        self.create_test_file("empty.hl7", "")
        self.create_test_file("malformed.hl7", "NOT_HL7_CONTENT")
        
        # Run pipeline
        orchestrator = PipelineOrchestrator(self.config)
        result = orchestrator.run_pipeline()
        
        # Pipeline should complete despite errors
        self.assertIsInstance(result, dict)
        stats = result['execution_summary']
        self.assertEqual(stats['total_files_processed'], 3)
        
        # Should have handled all files (success or quarantine)
        total_handled = (stats['files_successfully_enhanced'] + 
                        stats['files_already_valid'] + 
                        stats['files_quarantined'])
        self.assertEqual(total_handled, 3)
    
    def test_directory_structure_preservation(self):
        """Test that directory structure is preserved in output"""
        # Create nested directory structure
        subdir = self.source_dir / "subdir"
        subdir.mkdir()
        
        self.create_test_file("root_file.hl7", self.valid_adt_message)
        
        subfile_path = subdir / "sub_file.hl7"
        with open(subfile_path, 'w') as f:
            f.write(self.siu_message)
        
        # Run pipeline
        orchestrator = PipelineOrchestrator(self.config)
        result = orchestrator.run_pipeline()
        
        # Check that directory structure is preserved
        output_root_file = self.output_dir / "root_file.hl7"
        output_sub_file = self.output_dir / "subdir" / "sub_file.hl7"
        
        # At least one should exist (depending on processing success)
        output_files = list(self.output_dir.rglob("*.hl7"))
        self.assertGreater(len(output_files), 0)


if __name__ == '__main__':
    # Run integration tests
    unittest.main(verbosity=2)
