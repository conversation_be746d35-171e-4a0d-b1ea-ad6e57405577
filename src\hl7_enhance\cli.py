#!/usr/bin/env python3
"""
Command Line Interface for HL7 Enhancement Python Tool
"""

import argparse
import sys
import json
from pathlib import Path
from typing import Dict, Any

from .core.processor import HL7Processor
from .validators.enhanced_validator import EnhancedHL7Validator
from .converters.markdown_converter import HL7ToMarkdownConverter, ConversionConfig
from .pipeline.orchestrator import PipelineOrchestrator, PipelineConfig


def load_config(config_path: str = None) -> Dict[str, Any]:
    """Load configuration from file"""
    if config_path and Path(config_path).exists():
        with open(config_path, 'r') as f:
            return json.load(f)
    
    # Try default config
    default_config = Path("config/default_config.json")
    if default_config.exists():
        with open(default_config, 'r') as f:
            return json.load(f)
    
    # Return minimal default
    return {
        "directories": {
            "input": "data/input",
            "output": "data/output",
            "quarantine": "data/quarantine"
        }
    }


def cmd_process(args):
    """Process HL7 files using the core processor"""
    print("🚀 Starting HL7 Processing...")
    
    processor = HL7Processor(
        source_dir=args.input,
        output_dir=args.output,
        verbose=args.verbose
    )
    
    processor.run()
    print("✅ Processing completed!")


def cmd_validate(args):
    """Validate HL7 files"""
    print("🔍 Starting HL7 Validation...")
    
    validator = EnhancedHL7Validator()
    
    if args.file:
        # Validate single file
        result = validator.validate_file(args.file)
        print(f"File: {args.file}")
        print(f"Valid: {result.is_valid}")
        print(f"Message Type: {result.message_type}")
        print(f"Version: {result.version}")
        
        if result.issues:
            print("\nIssues found:")
            for issue in result.issues:
                print(f"  [{issue.severity.value}] {issue.code}: {issue.message}")
                if issue.fix_suggestion:
                    print(f"    Suggestion: {issue.fix_suggestion}")
    else:
        # Validate directory
        results = validator.validate_directory(args.input)
        
        valid_count = sum(1 for r in results if r.is_valid)
        total_count = len(results)
        
        print(f"Validation Results: {valid_count}/{total_count} files valid")
        
        # Show invalid files
        for result in results:
            if not result.is_valid:
                file_path = result.statistics.get('file_path', 'Unknown')
                print(f"\n❌ {file_path}")
                for issue in result.get_errors()[:3]:  # Show first 3 errors
                    print(f"   {issue.code}: {issue.message}")
    
    print("✅ Validation completed!")


def cmd_convert(args):
    """Convert HL7 files to Markdown"""
    print("📝 Starting HL7 to Markdown Conversion...")
    
    config = ConversionConfig(
        input_folder=args.input,
        output_folder=args.output,
        messages_per_file=args.batch_size,
        remove_duplicates=not args.keep_duplicates,
        include_source_info=not args.no_source_info
    )
    
    converter = HL7ToMarkdownConverter(config)
    
    if args.file:
        # Convert single file
        output_file = args.output_file or f"{Path(args.file).stem}.md"
        success = converter.convert_single_file(args.file, output_file)
        if success:
            print(f"✅ Converted {args.file} to {output_file}")
        else:
            print(f"❌ Failed to convert {args.file}")
    else:
        # Convert batch
        result = converter.convert_batch()
        print(f"✅ Conversion completed!")
        print(f"   Files processed: {result.total_files_processed}")
        print(f"   Unique messages: {result.unique_messages_found}")
        print(f"   Markdown files created: {result.markdown_files_created}")
        
        if result.errors_encountered:
            print(f"   Errors: {len(result.errors_encountered)}")


def cmd_pipeline(args):
    """Run the complete HL7 processing pipeline"""
    print("🔄 Starting HL7 Processing Pipeline...")
    
    config = PipelineConfig(
        source_directory=args.input,
        output_directory=args.output,
        quarantine_directory=args.quarantine,
        file_patterns=["*.hl7", "*.txt"],
        max_enhancement_retries=args.max_retries,
        enable_auto_retry=args.auto_retry,
        batch_size=args.batch_size,
        parallel_processing=args.parallel,
        backup_original_files=args.backup,
        generate_reports=args.reports,
        log_level=args.log_level.upper()
    )
    
    orchestrator = PipelineOrchestrator(config)
    result = orchestrator.run_pipeline()
    
    print("✅ Pipeline completed!")
    
    # Show summary
    stats = result['execution_summary']
    print(f"📊 Summary:")
    print(f"   Files processed: {stats['total_files_processed']}")
    print(f"   Successfully enhanced: {stats['files_successfully_enhanced']}")
    print(f"   Already valid: {stats['files_already_valid']}")
    print(f"   Quarantined: {stats['files_quarantined']}")
    print(f"   Processing time: {stats['processing_time_seconds']:.2f}s")


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="HL7 Enhancement Python Tool - Comprehensive HL7 processing pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process HL7 files
  hl7-enhance process --input data/input --output data/output

  # Validate HL7 files
  hl7-enhance validate --input data/input

  # Convert to Markdown
  hl7-enhance convert --input data/input --output data/markdown

  # Run complete pipeline
  hl7-enhance pipeline --input data/input --output data/output
        """
    )
    
    parser.add_argument('--version', action='version', version='%(prog)s 1.0.0')
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Process command
    process_parser = subparsers.add_parser('process', help='Process HL7 files')
    process_parser.add_argument('--input', '-i', required=True, help='Input directory')
    process_parser.add_argument('--output', '-o', required=True, help='Output directory')
    process_parser.set_defaults(func=cmd_process)
    
    # Validate command
    validate_parser = subparsers.add_parser('validate', help='Validate HL7 files')
    validate_parser.add_argument('--input', '-i', help='Input directory')
    validate_parser.add_argument('--file', '-f', help='Single file to validate')
    validate_parser.set_defaults(func=cmd_validate)
    
    # Convert command
    convert_parser = subparsers.add_parser('convert', help='Convert HL7 to Markdown')
    convert_parser.add_argument('--input', '-i', help='Input directory')
    convert_parser.add_argument('--output', '-o', required=True, help='Output directory')
    convert_parser.add_argument('--file', '-f', help='Single file to convert')
    convert_parser.add_argument('--output-file', help='Output file for single file conversion')
    convert_parser.add_argument('--batch-size', type=int, default=25, help='Messages per file')
    convert_parser.add_argument('--keep-duplicates', action='store_true', help='Keep duplicate messages')
    convert_parser.add_argument('--no-source-info', action='store_true', help='Exclude source file info')
    convert_parser.set_defaults(func=cmd_convert)
    
    # Pipeline command
    pipeline_parser = subparsers.add_parser('pipeline', help='Run complete processing pipeline')
    pipeline_parser.add_argument('--input', '-i', required=True, help='Input directory')
    pipeline_parser.add_argument('--output', '-o', required=True, help='Output directory')
    pipeline_parser.add_argument('--quarantine', '-q', help='Quarantine directory')
    pipeline_parser.add_argument('--max-retries', type=int, default=3, help='Max enhancement retries')
    pipeline_parser.add_argument('--auto-retry', action='store_true', help='Enable auto retry')
    pipeline_parser.add_argument('--batch-size', type=int, default=100, help='Batch size')
    pipeline_parser.add_argument('--parallel', action='store_true', help='Enable parallel processing')
    pipeline_parser.add_argument('--backup', action='store_true', help='Backup original files')
    pipeline_parser.add_argument('--reports', action='store_true', help='Generate reports')
    pipeline_parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    pipeline_parser.set_defaults(func=cmd_pipeline)
    
    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Load configuration
    config = load_config(args.config)
    
    # Set default quarantine directory if not specified
    if hasattr(args, 'quarantine') and not args.quarantine:
        args.quarantine = config.get('directories', {}).get('quarantine', 'data/quarantine')
    
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
