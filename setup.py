"""
Setup script for HL7 Enhancement Python Tool
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# Read requirements
requirements = []
requirements_file = this_directory / "requirements.txt"
if requirements_file.exists():
    requirements = requirements_file.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

# Read version from package
version = "1.0.0"
try:
    from src.hl7_enhance import __version__
    version = __version__
except ImportError:
    pass

setup(
    name="hl7-enhance-python",
    version=version,
    author="M.F.M Fazrin",
    author_email="<EMAIL>",
    description="A comprehensive Python-based HL7 message processing and enhancement engine",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/nirzaf/Hl7EnhancePython",
    project_urls={
        "Bug Tracker": "https://github.com/nirzaf/Hl7EnhancePython/issues",
        "Documentation": "https://github.com/nirzaf/Hl7EnhancePython/blob/main/README.md",
        "Source Code": "https://github.com/nirzaf/Hl7EnhancePython",
    },
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Healthcare Industry",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=1.0.0",
            "pre-commit>=2.20.0",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "pytest-mock>=3.10.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "hl7-enhance=src.hl7_enhance.cli:main",
            "hl7-validate=src.hl7_enhance.validators.cli:main",
            "hl7-convert=src.hl7_enhance.converters.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "hl7_enhance": [
            "config/*.json",
            "data/templates/*.json",
            "data/schemas/*.json",
        ],
    },
    zip_safe=False,
    keywords=[
        "hl7",
        "healthcare",
        "medical",
        "interoperability",
        "validation",
        "enhancement",
        "processing",
        "pipeline",
        "v2.8",
        "standards",
    ],
)
