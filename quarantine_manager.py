#!/usr/bin/env python3
"""
Advanced Quarantine Manager for HL7 messages
Manages messages that cannot be automatically fixed with detailed categorization and retry mechanisms
"""

import json
import logging
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
from enhanced_validator import ValidationResult, ValidationIssue, ValidationSeverity

class QuarantineCategory(Enum):
    """Categories for quarantined messages"""
    PARSING_ERROR = "PARSING_ERROR"
    ENCODING_ISSUES = "ENCODING_ISSUES"
    MISSING_SEGMENTS = "MISSING_SEGMENTS"
    INVALID_STRUCTURE = "INVALID_STRUCTURE"
    DATA_INTEGRITY = "DATA_INTEGRITY"
    UNKNOWN_MESSAGE_TYPE = "UNKNOWN_MESSAGE_TYPE"
    MANUAL_REVIEW_REQUIRED = "MANUAL_REVIEW_REQUIRED"

class QuarantineReason(Enum):
    """Specific reasons for quarantine"""
    ENHANCEMENT_FAILED = "ENHANCEMENT_FAILED"
    VALIDATION_FAILED = "VALIDATION_FAILED"
    PARSING_FAILED = "PARSING_FAILED"
    CRITICAL_DATA_MISSING = "CRITICAL_DATA_MISSING"
    UNSUPPORTED_MESSAGE_TYPE = "UNSUPPORTED_MESSAGE_TYPE"
    MULTIPLE_ENHANCEMENT_FAILURES = "MULTIPLE_ENHANCEMENT_FAILURES"

@dataclass
class QuarantineRecord:
    """Record of a quarantined message"""
    file_path: str
    original_path: str
    quarantine_timestamp: str
    category: QuarantineCategory
    reason: QuarantineReason
    validation_issues: List[Dict]
    enhancement_attempts: int
    last_enhancement_attempt: Optional[str]
    retry_count: int
    next_retry_time: Optional[str]
    manual_review_required: bool
    fix_recommendations: List[str]
    metadata: Dict[str, Any]

@dataclass
class QuarantineStats:
    """Statistics for quarantined messages"""
    total_quarantined: int
    by_category: Dict[str, int]
    by_reason: Dict[str, int]
    retry_eligible: int
    manual_review_required: int
    oldest_quarantine: Optional[str]
    newest_quarantine: Optional[str]

class QuarantineManager:
    """Advanced quarantine manager for HL7 messages"""
    
    def __init__(self, quarantine_dir: str, config: Optional[Dict] = None):
        """Initialize the quarantine manager"""
        self.logger = logging.getLogger(__name__)
        self.quarantine_dir = Path(quarantine_dir)
        self.quarantine_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration
        self.config = config or {}
        self.max_retry_attempts = self.config.get('max_retry_attempts', 3)
        self.retry_delay_hours = self.config.get('retry_delay_hours', 24)
        self.auto_retry_enabled = self.config.get('auto_retry_enabled', True)
        
        # Quarantine subdirectories
        self.categories_dir = self.quarantine_dir / "categories"
        self.metadata_dir = self.quarantine_dir / "metadata"
        self.reports_dir = self.quarantine_dir / "reports"
        
        # Create subdirectories
        for dir_path in [self.categories_dir, self.metadata_dir, self.reports_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Create category subdirectories
        for category in QuarantineCategory:
            (self.categories_dir / category.value).mkdir(parents=True, exist_ok=True)
    
    def quarantine_message(self, 
                          file_path: str, 
                          validation_result: ValidationResult,
                          enhancement_attempts: int = 0,
                          enhancement_errors: Optional[List[str]] = None) -> QuarantineRecord:
        """Quarantine a message with detailed categorization"""
        
        # Determine category and reason
        category, reason = self._categorize_quarantine(validation_result, enhancement_attempts)
        
        # Generate quarantine record
        timestamp = datetime.utcnow().isoformat() + 'Z'
        quarantine_record = QuarantineRecord(
            file_path=file_path,
            original_path=file_path,
            quarantine_timestamp=timestamp,
            category=category,
            reason=reason,
            validation_issues=[self._serialize_issue(issue) for issue in validation_result.issues],
            enhancement_attempts=enhancement_attempts,
            last_enhancement_attempt=timestamp if enhancement_attempts > 0 else None,
            retry_count=0,
            next_retry_time=self._calculate_next_retry_time() if self.auto_retry_enabled else None,
            manual_review_required=self._requires_manual_review(category, reason),
            fix_recommendations=self._generate_fix_recommendations(validation_result),
            metadata={
                'message_type': validation_result.message_type,
                'version': validation_result.version,
                'file_size': self._get_file_size(file_path),
                'enhancement_errors': enhancement_errors or []
            }
        )
        
        # Move file to quarantine
        quarantine_path = self._move_to_quarantine(file_path, category)
        quarantine_record.file_path = str(quarantine_path)
        
        # Save metadata
        self._save_quarantine_metadata(quarantine_record)
        
        # Log quarantine
        self.logger.warning(f"Quarantined message: {file_path} -> {category.value} ({reason.value})")
        
        return quarantine_record
    
    def _categorize_quarantine(self, validation_result: ValidationResult, enhancement_attempts: int) -> tuple:
        """Categorize the quarantine based on validation issues"""
        
        # Check for parsing errors
        parsing_errors = [issue for issue in validation_result.issues 
                         if issue.code in ['PARSING_ERROR', 'FILE_READ_ERROR', 'EMPTY_FILE']]
        if parsing_errors:
            return QuarantineCategory.PARSING_ERROR, QuarantineReason.PARSING_FAILED
        
        # Check for encoding issues
        encoding_errors = [issue for issue in validation_result.issues 
                          if issue.code in ['INVALID_ENCODING_CHARACTERS']]
        if encoding_errors and enhancement_attempts > 0:
            return QuarantineCategory.ENCODING_ISSUES, QuarantineReason.ENHANCEMENT_FAILED
        
        # Check for missing segments
        missing_segments = [issue for issue in validation_result.issues 
                           if issue.code in ['MISSING_REQUIRED_SEGMENT', 'MISSING_EVN_SEGMENT']]
        if missing_segments and enhancement_attempts > 0:
            return QuarantineCategory.MISSING_SEGMENTS, QuarantineReason.ENHANCEMENT_FAILED
        
        # Check for unknown message types
        unknown_type = [issue for issue in validation_result.issues 
                       if issue.code in ['UNKNOWN_MESSAGE_TYPE']]
        if unknown_type:
            return QuarantineCategory.UNKNOWN_MESSAGE_TYPE, QuarantineReason.UNSUPPORTED_MESSAGE_TYPE
        
        # Check for critical data missing
        critical_missing = [issue for issue in validation_result.issues 
                           if issue.code in ['MISSING_PATIENT_ID'] and issue.severity == ValidationSeverity.ERROR]
        if critical_missing:
            return QuarantineCategory.DATA_INTEGRITY, QuarantineReason.CRITICAL_DATA_MISSING
        
        # Multiple enhancement failures
        if enhancement_attempts >= self.max_retry_attempts:
            return QuarantineCategory.MANUAL_REVIEW_REQUIRED, QuarantineReason.MULTIPLE_ENHANCEMENT_FAILURES
        
        # Default categorization
        if enhancement_attempts > 0:
            return QuarantineCategory.INVALID_STRUCTURE, QuarantineReason.ENHANCEMENT_FAILED
        else:
            return QuarantineCategory.INVALID_STRUCTURE, QuarantineReason.VALIDATION_FAILED
    
    def _serialize_issue(self, issue: ValidationIssue) -> Dict:
        """Serialize a validation issue to dictionary"""
        return {
            'severity': issue.severity.value,
            'code': issue.code,
            'message': issue.message,
            'segment': issue.segment,
            'field': issue.field,
            'location': issue.location,
            'fix_suggestion': issue.fix_suggestion
        }
    
    def _calculate_next_retry_time(self) -> str:
        """Calculate next retry time"""
        next_retry = datetime.utcnow() + timedelta(hours=self.retry_delay_hours)
        return next_retry.isoformat() + 'Z'
    
    def _requires_manual_review(self, category: QuarantineCategory, reason: QuarantineReason) -> bool:
        """Determine if manual review is required"""
        manual_review_categories = {
            QuarantineCategory.DATA_INTEGRITY,
            QuarantineCategory.UNKNOWN_MESSAGE_TYPE,
            QuarantineCategory.MANUAL_REVIEW_REQUIRED
        }
        
        manual_review_reasons = {
            QuarantineReason.CRITICAL_DATA_MISSING,
            QuarantineReason.UNSUPPORTED_MESSAGE_TYPE,
            QuarantineReason.MULTIPLE_ENHANCEMENT_FAILURES
        }
        
        return category in manual_review_categories or reason in manual_review_reasons
    
    def _generate_fix_recommendations(self, validation_result: ValidationResult) -> List[str]:
        """Generate fix recommendations based on validation issues"""
        recommendations = []
        
        for issue in validation_result.get_errors():
            if issue.fix_suggestion:
                recommendations.append(f"{issue.code}: {issue.fix_suggestion}")
        
        # Add general recommendations
        if validation_result.message_type == "UNKNOWN":
            recommendations.append("Verify message type in MSH.9 field")
        
        if any(issue.code == 'PARSING_ERROR' for issue in validation_result.issues):
            recommendations.append("Check message format and encoding (should be UTF-8)")
            recommendations.append("Verify segment separators (should be \\r or \\n)")
        
        return list(set(recommendations))  # Remove duplicates
    
    def _get_file_size(self, file_path: str) -> int:
        """Get file size in bytes"""
        try:
            return Path(file_path).stat().st_size
        except:
            return 0
    
    def _move_to_quarantine(self, file_path: str, category: QuarantineCategory) -> Path:
        """Move file to appropriate quarantine category directory"""
        source_path = Path(file_path)
        category_dir = self.categories_dir / category.value
        
        # Generate unique filename to avoid conflicts
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        quarantine_filename = f"{timestamp}_{source_path.name}"
        quarantine_path = category_dir / quarantine_filename
        
        # Copy file to quarantine
        shutil.copy2(source_path, quarantine_path)
        
        return quarantine_path
    
    def _save_quarantine_metadata(self, record: QuarantineRecord):
        """Save quarantine metadata"""
        metadata_file = self.metadata_dir / f"{Path(record.file_path).stem}.json"
        
        with open(metadata_file, 'w') as f:
            json.dump(asdict(record), f, indent=2, default=str)
    
    def get_quarantine_stats(self) -> QuarantineStats:
        """Get comprehensive quarantine statistics"""
        metadata_files = list(self.metadata_dir.glob("*.json"))
        
        total_quarantined = len(metadata_files)
        by_category = {category.value: 0 for category in QuarantineCategory}
        by_reason = {reason.value: 0 for reason in QuarantineReason}
        retry_eligible = 0
        manual_review_required = 0
        timestamps = []
        
        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    record_data = json.load(f)
                
                category = record_data.get('category')
                reason = record_data.get('reason')
                
                if category:
                    by_category[category] = by_category.get(category, 0) + 1
                if reason:
                    by_reason[reason] = by_reason.get(reason, 0) + 1
                
                if record_data.get('next_retry_time') and not record_data.get('manual_review_required'):
                    retry_eligible += 1
                
                if record_data.get('manual_review_required'):
                    manual_review_required += 1
                
                if record_data.get('quarantine_timestamp'):
                    timestamps.append(record_data['quarantine_timestamp'])
                    
            except Exception as e:
                self.logger.error(f"Error reading metadata file {metadata_file}: {e}")
        
        return QuarantineStats(
            total_quarantined=total_quarantined,
            by_category=by_category,
            by_reason=by_reason,
            retry_eligible=retry_eligible,
            manual_review_required=manual_review_required,
            oldest_quarantine=min(timestamps) if timestamps else None,
            newest_quarantine=max(timestamps) if timestamps else None
        )

    def get_retry_eligible_messages(self) -> List[QuarantineRecord]:
        """Get messages eligible for retry"""
        eligible_messages = []
        current_time = datetime.utcnow()

        metadata_files = list(self.metadata_dir.glob("*.json"))

        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    record_data = json.load(f)

                # Skip if manual review required
                if record_data.get('manual_review_required'):
                    continue

                # Skip if max retries exceeded
                if record_data.get('retry_count', 0) >= self.max_retry_attempts:
                    continue

                # Check if retry time has passed
                next_retry_str = record_data.get('next_retry_time')
                if next_retry_str:
                    next_retry_time = datetime.fromisoformat(next_retry_str.replace('Z', '+00:00'))
                    if current_time >= next_retry_time.replace(tzinfo=None):
                        # Convert dict back to QuarantineRecord
                        record = QuarantineRecord(**{
                            k: QuarantineCategory(v) if k == 'category' else
                               QuarantineReason(v) if k == 'reason' else v
                            for k, v in record_data.items()
                        })
                        eligible_messages.append(record)

            except Exception as e:
                self.logger.error(f"Error processing metadata file {metadata_file}: {e}")

        return eligible_messages

    def update_retry_attempt(self, record: QuarantineRecord, success: bool = False):
        """Update retry attempt information"""
        record.retry_count += 1
        record.last_enhancement_attempt = datetime.utcnow().isoformat() + 'Z'

        if not success and record.retry_count < self.max_retry_attempts:
            # Schedule next retry
            record.next_retry_time = self._calculate_next_retry_time()
        else:
            # No more retries
            record.next_retry_time = None
            if not success:
                record.manual_review_required = True
                record.reason = QuarantineReason.MULTIPLE_ENHANCEMENT_FAILURES

        # Save updated metadata
        self._save_quarantine_metadata(record)

    def generate_quarantine_report(self) -> Dict:
        """Generate comprehensive quarantine report"""
        stats = self.get_quarantine_stats()
        retry_eligible = self.get_retry_eligible_messages()

        # Get detailed records for manual review
        manual_review_records = []
        metadata_files = list(self.metadata_dir.glob("*.json"))

        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    record_data = json.load(f)

                if record_data.get('manual_review_required'):
                    manual_review_records.append({
                        'file_path': record_data.get('file_path'),
                        'category': record_data.get('category'),
                        'reason': record_data.get('reason'),
                        'quarantine_timestamp': record_data.get('quarantine_timestamp'),
                        'fix_recommendations': record_data.get('fix_recommendations', []),
                        'validation_issues': record_data.get('validation_issues', [])
                    })

            except Exception as e:
                self.logger.error(f"Error reading metadata file {metadata_file}: {e}")

        return {
            'report_timestamp': datetime.utcnow().isoformat() + 'Z',
            'statistics': asdict(stats),
            'retry_eligible_count': len(retry_eligible),
            'manual_review_required': manual_review_records,
            'recommendations': self._generate_global_recommendations(stats),
            'quarantine_trends': self._analyze_quarantine_trends()
        }

    def _generate_global_recommendations(self, stats: QuarantineStats) -> List[str]:
        """Generate global recommendations based on quarantine patterns"""
        recommendations = []

        # Analyze most common categories
        if stats.by_category.get('ENCODING_ISSUES', 0) > 0:
            recommendations.append("Consider implementing pre-processing to fix encoding character issues")

        if stats.by_category.get('MISSING_SEGMENTS', 0) > 0:
            recommendations.append("Review message generation process to ensure required segments are included")

        if stats.by_category.get('PARSING_ERROR', 0) > 0:
            recommendations.append("Validate message format and encoding at source systems")

        if stats.by_reason.get('MULTIPLE_ENHANCEMENT_FAILURES', 0) > 0:
            recommendations.append("Review enhancement strategies for complex validation failures")

        if stats.manual_review_required > stats.total_quarantined * 0.1:  # More than 10%
            recommendations.append("High manual review rate - consider expanding automated enhancement capabilities")

        return recommendations

    def _analyze_quarantine_trends(self) -> Dict:
        """Analyze quarantine trends over time"""
        # This is a simplified trend analysis
        # In a production system, you might want more sophisticated time-series analysis

        metadata_files = list(self.metadata_dir.glob("*.json"))
        daily_counts = {}
        category_trends = {}

        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    record_data = json.load(f)

                timestamp = record_data.get('quarantine_timestamp')
                category = record_data.get('category')

                if timestamp:
                    # Extract date (YYYY-MM-DD)
                    date = timestamp.split('T')[0]
                    daily_counts[date] = daily_counts.get(date, 0) + 1

                    if category:
                        if date not in category_trends:
                            category_trends[date] = {}
                        category_trends[date][category] = category_trends[date].get(category, 0) + 1

            except Exception as e:
                self.logger.error(f"Error analyzing trends from {metadata_file}: {e}")

        return {
            'daily_quarantine_counts': daily_counts,
            'category_trends_by_date': category_trends,
            'total_days_with_quarantines': len(daily_counts),
            'average_daily_quarantines': sum(daily_counts.values()) / len(daily_counts) if daily_counts else 0
        }

    def export_quarantine_report(self, output_path: str, format: str = 'json'):
        """Export quarantine report to file"""
        report = self.generate_quarantine_report()
        output_file = Path(output_path)

        if format.lower() == 'json':
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
        elif format.lower() == 'csv':
            # Export summary statistics as CSV
            import csv
            with open(output_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['Metric', 'Value'])
                writer.writerow(['Total Quarantined', report['statistics']['total_quarantined']])
                writer.writerow(['Retry Eligible', report['retry_eligible_count']])
                writer.writerow(['Manual Review Required', report['statistics']['manual_review_required']])

                writer.writerow([])
                writer.writerow(['Category', 'Count'])
                for category, count in report['statistics']['by_category'].items():
                    writer.writerow([category, count])

        self.logger.info(f"Quarantine report exported to {output_file}")

    def cleanup_resolved_quarantines(self, days_old: int = 30):
        """Clean up old quarantine records that have been resolved"""
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        metadata_files = list(self.metadata_dir.glob("*.json"))
        cleaned_count = 0

        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    record_data = json.load(f)

                quarantine_time_str = record_data.get('quarantine_timestamp')
                if quarantine_time_str:
                    quarantine_time = datetime.fromisoformat(quarantine_time_str.replace('Z', '+00:00'))

                    # Clean up old records that don't require manual review and have exceeded retry attempts
                    if (quarantine_time.replace(tzinfo=None) < cutoff_date and
                        not record_data.get('manual_review_required') and
                        record_data.get('retry_count', 0) >= self.max_retry_attempts):

                        # Remove metadata file
                        metadata_file.unlink()

                        # Remove quarantined message file
                        quarantine_file = Path(record_data.get('file_path', ''))
                        if quarantine_file.exists():
                            quarantine_file.unlink()

                        cleaned_count += 1

            except Exception as e:
                self.logger.error(f"Error cleaning up {metadata_file}: {e}")

        self.logger.info(f"Cleaned up {cleaned_count} old quarantine records")
        return cleaned_count
